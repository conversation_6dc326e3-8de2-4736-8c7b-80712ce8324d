import React, { useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';

interface BioQuantumFlowProps {
  isActive: boolean;
  variant?: 'neural' | 'cellular' | 'molecular';
  intensity?: 'low' | 'medium' | 'high';
  className?: string;
}

const BioQuantumFlow: React.FC<BioQuantumFlowProps> = ({
  isActive,
  variant = 'neural',
  intensity = 'medium',
  className
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const nodesRef = useRef<Array<{
    x: number;
    y: number;
    vx: number;
    vy: number;
    energy: number;
    connections: number[];
    pulsePhase: number;
  }>>([]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const resizeCanvas = () => {
      const rect = canvas.getBoundingClientRect();
      canvas.width = rect.width * 2;
      canvas.height = rect.height * 2;
      ctx.scale(2, 2);
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Initialize bio-quantum nodes
    const initializeNodes = () => {
      const nodeCount = {
        low: 15,
        medium: 25,
        high: 40
      }[intensity];

      nodesRef.current = [];
      
      for (let i = 0; i < nodeCount; i++) {
        const node = {
          x: Math.random() * canvas.width / 2,
          y: Math.random() * canvas.height / 2,
          vx: (Math.random() - 0.5) * 0.5,
          vy: (Math.random() - 0.5) * 0.5,
          energy: Math.random() * 100 + 50,
          connections: [] as number[],
          pulsePhase: Math.random() * Math.PI * 2
        };
        
        nodesRef.current.push(node);
      }

      // Create connections based on proximity
      nodesRef.current.forEach((node, index) => {
        nodesRef.current.forEach((otherNode, otherIndex) => {
          if (index !== otherIndex) {
            const dx = node.x - otherNode.x;
            const dy = node.y - otherNode.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            
            if (distance < 120 && node.connections.length < 3) {
              node.connections.push(otherIndex);
            }
          }
        });
      });
    };

    initializeNodes();

    let time = 0;

    const animate = () => {
      if (!isActive) {
        animationRef.current = requestAnimationFrame(animate);
        return;
      }

      ctx.clearRect(0, 0, canvas.width / 2, canvas.height / 2);
      
      time += 0.02;

      // Update node positions and energy
      nodesRef.current.forEach((node, index) => {
        // Bio-inspired movement patterns
        switch (variant) {
          case 'neural':
            // Synaptic firing patterns
            node.x += node.vx + Math.sin(time * 2 + node.pulsePhase) * 0.3;
            node.y += node.vy + Math.cos(time * 2 + node.pulsePhase) * 0.3;
            break;
          case 'cellular':
            // Cellular division patterns
            node.x += node.vx + Math.sin(time + index * 0.5) * 0.2;
            node.y += node.vy + Math.cos(time + index * 0.5) * 0.2;
            break;
          case 'molecular':
            // Brownian motion with quantum effects
            node.x += node.vx + (Math.random() - 0.5) * 0.5;
            node.y += node.vy + (Math.random() - 0.5) * 0.5;
            break;
        }

        // Boundary reflection
        if (node.x < 0 || node.x > canvas.width / 2) node.vx *= -1;
        if (node.y < 0 || node.y > canvas.height / 2) node.vy *= -1;
        
        // Keep nodes in bounds
        node.x = Math.max(0, Math.min(canvas.width / 2, node.x));
        node.y = Math.max(0, Math.min(canvas.height / 2, node.y));

        // Energy oscillation
        node.energy = 50 + Math.sin(time * 3 + node.pulsePhase) * 40;
      });

      // Render connections (quantum entanglement)
      ctx.strokeStyle = 'rgba(0, 255, 170, 0.3)';
      ctx.lineWidth = 1;
      
      nodesRef.current.forEach((node, index) => {
        node.connections.forEach(connectionIndex => {
          const connectedNode = nodesRef.current[connectionIndex];
          if (connectedNode) {
            const energyFlow = (Math.sin(time * 4 + index) + 1) * 0.5;
            const alpha = 0.2 + energyFlow * 0.6;
            
            ctx.strokeStyle = `rgba(0, 255, 170, ${alpha})`;
            ctx.shadowBlur = 4;
            ctx.shadowColor = 'rgba(0, 255, 170, 0.5)';
            
            ctx.beginPath();
            ctx.moveTo(node.x, node.y);
            
            // Curved connection for organic feel
            const midX = (node.x + connectedNode.x) / 2;
            const midY = (node.y + connectedNode.y) / 2;
            const curvature = Math.sin(time * 2 + index) * 20;
            
            ctx.quadraticCurveTo(
              midX + curvature,
              midY + curvature,
              connectedNode.x,
              connectedNode.y
            );
            ctx.stroke();

            // Energy pulse along connection
            const pulsePosition = (Math.sin(time * 6 + index) + 1) * 0.5;
            const pulseX = node.x + (connectedNode.x - node.x) * pulsePosition;
            const pulseY = node.y + (connectedNode.y - node.y) * pulsePosition;
            
            ctx.shadowBlur = 8;
            ctx.fillStyle = 'rgba(0, 255, 255, 0.8)';
            ctx.beginPath();
            ctx.arc(pulseX, pulseY, 2, 0, Math.PI * 2);
            ctx.fill();
          }
        });
      });

      // Render nodes (bio-quantum entities)
      nodesRef.current.forEach((node, index) => {
        const energyRatio = node.energy / 100;
        const nodeRadius = 4 + energyRatio * 6;
        
        // Bio-quantum halo
        ctx.shadowBlur = 12;
        ctx.shadowColor = 'rgba(0, 255, 170, 0.6)';
        ctx.fillStyle = `rgba(0, 255, 170, ${0.6 + energyRatio * 0.4})`;
        
        ctx.beginPath();
        ctx.arc(node.x, node.y, nodeRadius * 1.5, 0, Math.PI * 2);
        ctx.fill();
        
        // Core node
        ctx.shadowBlur = 6;
        ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        ctx.beginPath();
        ctx.arc(node.x, node.y, nodeRadius * 0.5, 0, Math.PI * 2);
        ctx.fill();

        // DNA-inspired spiral around active nodes
        if (energyRatio > 0.8) {
          ctx.strokeStyle = 'rgba(170, 0, 255, 0.6)';
          ctx.lineWidth = 2;
          ctx.shadowBlur = 4;
          
          for (let spiral = 0; spiral < 2; spiral++) {
            ctx.beginPath();
            for (let angle = 0; angle <= Math.PI * 4; angle += 0.2) {
              const spiralRadius = nodeRadius * 2 + (angle / (Math.PI * 4)) * 15;
              const spiralAngle = angle + time * 3 + spiral * Math.PI;
              const sx = node.x + Math.cos(spiralAngle) * spiralRadius;
              const sy = node.y + Math.sin(spiralAngle) * spiralRadius;
              
              if (angle === 0) ctx.moveTo(sx, sy);
              else ctx.lineTo(sx, sy);
            }
            ctx.stroke();
          }
        }
      });

      ctx.shadowBlur = 0;
      animationRef.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isActive, variant, intensity]);

  return (
    <div className={cn("relative w-full h-full overflow-hidden", className)}>
      <canvas
        ref={canvasRef}
        className="w-full h-full"
        style={{
          background: `radial-gradient(ellipse at center, 
            rgba(0, 255, 170, 0.05) 0%, 
            rgba(170, 0, 255, 0.03) 50%, 
            transparent 70%)`
        }}
      />
      
      {/* Bio-quantum field indicator */}
      <div className="absolute bottom-4 right-4 text-xs text-consciousness/60 font-mono">
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 rounded-full bg-consciousness animate-pulse" />
          <span>{variant.charAt(0).toUpperCase() + variant.slice(1)} Field Active</span>
        </div>
      </div>
    </div>
  );
};

export default BioQuantumFlow;
