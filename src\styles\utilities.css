
/* Utility Classes */
@layer utilities {
  /* Division Color Utilities */
  .text-consciousness { color: var(--consciousness-primary); }
  .text-symbioautomate { color: var(--symbioautomate-primary); }
  .text-symbiolabs { color: var(--symbiolabs-primary); }
  .text-symbioxchange { color: var(--symbioxchange-primary); }
  .text-symbioedge { color: var(--symbioedge-primary); }
  .text-symbioimpact { color: var(--symbioimpact-primary); }
  .text-symbioventures { color: var(--symbioventures-primary); }
  .text-symbioalliance { color: var(--symbioalliance-primary); }

  /* Background Utilities */
  .bg-abyssal-void { background-color: var(--abyssal-void); }
  .bg-abyssal-base { background-color: var(--abyssal-base); }
  .bg-abyssal-deep { background-color: var(--abyssal-deep); }
  .bg-abyssal-elevated { background-color: var(--abyssal-elevated); }

  /* Glow Effects */
  .glow-consciousness { 
    box-shadow: 0 0 20px var(--consciousness-glow); 
    animation: sentient-glow 3s ease-in-out infinite;
  }
  
  .glow-symbioautomate { box-shadow: 0 0 20px var(--symbioautomate-glow); }
  .glow-symbiolabs { box-shadow: 0 0 20px var(--symbiolabs-glow); }

  /* Morphic Animations */
  .animate-morphic-pulse {
    animation: morphic-pulse 4s ease-in-out infinite;
  }

  /* Bio-Quantum Animation Utilities */
  .animate-dna-helix {
    animation: dna-helix-rotation 10s linear infinite;
  }

  .animate-quantum-flicker {
    animation: quantum-flicker 2s ease-in-out infinite;
  }

  .animate-neural-pulse {
    animation: neural-pulse 3s ease-in-out infinite;
  }

  .animate-consciousness-wave {
    background: linear-gradient(270deg, 
      var(--consciousness-primary), 
      var(--consciousness-secondary), 
      var(--consciousness-primary));
    background-size: 200% 200%;
    animation: consciousness-wave 6s ease-in-out infinite;
  }

  .animate-bio-quantum-flow {
    background: linear-gradient(45deg, 
      transparent 0%, 
      var(--consciousness-glow) 25%, 
      transparent 50%, 
      var(--symbioautomate-glow) 75%, 
      transparent 100%);
    background-size: 400% 400%;
    animation: bio-quantum-flow 8s ease-in-out infinite;
  }

  /* Quantum Shadow Effects */
  .shadow-quantum-subtle {
    box-shadow: 0 4px 20px rgba(0, 255, 170, 0.15), 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .shadow-quantum-medium {
    box-shadow: 0 8px 30px rgba(0, 255, 170, 0.25), 0 4px 15px rgba(0, 0, 0, 0.15);
  }

  .shadow-quantum-intense {
    box-shadow: 
      0 12px 40px rgba(0, 255, 170, 0.4), 
      0 6px 20px rgba(0, 0, 0, 0.2),
      0 0 30px rgba(0, 229, 255, 0.3);
  }

  .shadow-division-auto {
    box-shadow: 0 8px 30px rgba(0, 255, 127, 0.25), 0 4px 15px rgba(0, 0, 0, 0.15);
  }

  .shadow-division-labs {
    box-shadow: 0 8px 30px rgba(255, 215, 0, 0.25), 0 4px 15px rgba(0, 0, 0, 0.15);
  }

  .shadow-sentient-glow {
    box-shadow: 0 0 20px var(--consciousness-glow);
    animation: sentient-glow 3s ease-in-out infinite;
  }

  /* Text Hierarchy */
  .text-primary { color: var(--text-primary); }
  .text-secondary { color: var(--text-secondary); }
  .text-tertiary { color: var(--text-tertiary); }
  .text-quaternary { color: var(--text-quaternary); }
  .text-quantum { color: var(--text-quantum); }

  /* Custom Slider Styles */
  .slider::-webkit-slider-thumb {
    appearance: none;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: var(--consciousness-primary);
    border: 2px solid var(--consciousness-secondary);
    cursor: pointer;
    box-shadow: 0 0 10px var(--consciousness-glow);
  }

  .slider::-moz-range-thumb {
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: var(--consciousness-primary);
    border: 2px solid var(--consciousness-secondary);
    cursor: pointer;
    box-shadow: 0 0 10px var(--consciousness-glow);
  }
}
