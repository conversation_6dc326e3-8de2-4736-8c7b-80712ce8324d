
import React from 'react';
import Header from '../components/organisms/Header';
import Footer from '../components/organisms/Footer';
import Typography from '../components/atoms/Typography';
import Card from '../components/atoms/Card';
import Button from '../components/atoms/Button';
import { Brain, Zap, Network, Cpu, ArrowRight } from 'lucide-react';

const ACITechnology: React.FC = () => {
  return (
    <div className="min-h-screen surface-void">
      <Header />
      
      <main className="pt-24">
        <div className="container mx-auto px-8 py-16">
          <div className="text-center mb-16">
            <Typography as="h1" variant="4xl" weight="bold" gradient="consciousness" className="mb-6">
              Artificial Cellular Intelligence
            </Typography>
            <Typography as="p" variant="xl" color="secondary" className="max-w-4xl mx-auto">
              Discover the revolutionary AI paradigm that's reshaping how intelligent systems learn, adapt, and evolve.
            </Typography>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            <Card variant="consciousness" className="p-8 text-center rounded-[24px] border-consciousness/25">
              <Brain className="w-16 h-16 mx-auto mb-6 text-consciousness" />
              <Typography variant="lg" weight="semibold" color="consciousness" className="mb-4">
                ACI Overview
              </Typography>
              <Typography variant="sm" color="secondary">
                Core principles of cellular intelligence
              </Typography>
            </Card>

            <Card variant="creativity" className="p-8 text-center rounded-[24px] border-creativity/25">
              <Network className="w-16 h-16 mx-auto mb-6 text-creativity" />
              <Typography variant="lg" weight="semibold" color="creativity" className="mb-4">
                DNA Architecture
              </Typography>
              <Typography variant="sm" color="secondary">
                Self-replicating system design
              </Typography>
            </Card>

            <Card variant="intuition" className="p-8 text-center rounded-[24px] border-harmony/25">
              <Cpu className="w-16 h-16 mx-auto mb-6 text-harmony" />
              <Typography variant="lg" weight="semibold" color="harmony" className="mb-4">
                Quantum Processing
              </Typography>
              <Typography variant="sm" color="secondary">
                Advanced computational methods
              </Typography>
            </Card>

            <Card variant="neural" className="p-8 text-center rounded-[24px] border-consciousness/25">
              <Zap className="w-16 h-16 mx-auto mb-6 text-consciousness" />
              <Typography variant="lg" weight="semibold" color="consciousness" className="mb-4">
                Bio-Mimetic Networks
              </Typography>
              <Typography variant="sm" color="secondary">
                Nature-inspired intelligence
              </Typography>
            </Card>
          </div>

          <div className="glass-quantum rounded-[32px] p-12 border border-consciousness/30">
            <Typography as="h2" variant="2xl" weight="semibold" color="consciousness" className="mb-8">
              The Future of Intelligent Systems
            </Typography>
            <Typography variant="lg" color="secondary" className="leading-relaxed mb-8">
              ACI represents a fundamental shift from traditional AI architectures. Instead of monolithic models,
              ACI creates ecosystems of intelligent agents that collaborate, compete, and evolve—just like biological cells.
              This approach enables unprecedented adaptability, resilience, and emergent intelligence.
            </Typography>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant="quantum"
                size="lg"
                leftIcon={<Network className="w-5 h-5" />}
                rightIcon={<ArrowRight className="w-5 h-5" />}
                onClick={() => window.location.href = '/dna-architecture'}
              >
                Explore DNA Architecture
              </Button>

              <Button
                variant="secondary"
                size="lg"
                leftIcon={<Cpu className="w-5 h-5" />}
                onClick={() => window.location.href = '/quantum-processing'}
              >
                Quantum Processing
              </Button>

              <Button
                variant="outline-quantum"
                size="lg"
                leftIcon={<Brain className="w-5 h-5" />}
                onClick={() => window.location.href = '/biomimetic-networks'}
              >
                Bio-Mimetic Networks
              </Button>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default ACITechnology;
