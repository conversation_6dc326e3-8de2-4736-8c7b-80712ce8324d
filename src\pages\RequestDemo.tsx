import React, { useState } from 'react';
import Header from '../components/organisms/Header';
import Footer from '../components/organisms/Footer';
import Typography from '../components/atoms/Typography';
import Button from '../components/atoms/Button';
import Card from '../components/atoms/Card';
import SentientButton from '../components/atoms/SentientButton';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Textarea } from '../components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import { ArrowRight, CheckCircle, Calendar, Users, Zap, Shield } from 'lucide-react';

const RequestDemo: React.FC = () => {
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    company: '',
    role: '',
    phone: '',
    employees: '',
    industry: '',
    challenges: '',
    timeline: ''
  });
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Simulate form submission
    setTimeout(() => {
      setIsSubmitted(true);
    }, 1000);
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  if (isSubmitted) {
    return (
      <div className="min-h-screen surface-void">
        <Header />
        <main className="pt-32 pb-20">
          <div className="container mx-auto px-6 text-center">
            <div className="max-w-2xl mx-auto">
              <div className="w-20 h-20 mx-auto mb-6 rounded-full bg-harmony/20 flex items-center justify-center">
                <CheckCircle className="w-10 h-10 text-harmony" />
              </div>
              
              <Typography variant="3xl" weight="bold" color="consciousness" className="mb-4">
                Demo Request Received!
              </Typography>
              
              <Typography variant="lg" color="secondary" className="mb-8">
                Thank you for your interest in SymbioAutomate. Our team will contact you within 24 hours to schedule your personalized demonstration.
              </Typography>
              
              <div className="space-y-4 mb-8">
                <div className="flex items-center justify-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-harmony" />
                  <Typography variant="sm" color="secondary">
                    Personalized demo preparation in progress
                  </Typography>
                </div>
                <div className="flex items-center justify-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-harmony" />
                  <Typography variant="sm" color="secondary">
                    Industry-specific use cases being prepared
                  </Typography>
                </div>
                <div className="flex items-center justify-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-harmony" />
                  <Typography variant="sm" color="secondary">
                    ROI calculator ready for your review
                  </Typography>
                </div>
              </div>
              
              <SentientButton
                variant="quantum"
                size="lg"
                glow="intense"
                onClick={() => window.location.href = '/symbioautomate'}
              >
                Return to SymbioAutomate
              </SentientButton>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen surface-void">
      <Header />
      
      <main className="pt-32 pb-20">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            {/* Header */}
            <div className="text-center mb-16">
              <Typography variant="4xl" weight="bold" gradient="consciousness" className="mb-6">
                Request Your Personalized Demo
              </Typography>
              <Typography variant="xl" color="secondary" className="mb-8 max-w-3xl mx-auto">
                Experience SymbioAutomate in action with a tailored demonstration designed specifically for your industry and use cases.
              </Typography>
            </div>

            <div className="grid lg:grid-cols-2 gap-12">
              {/* Form */}
              <Card variant="quantum" className="p-8 border-consciousness/30">
                <Typography variant="2xl" weight="bold" color="consciousness" className="mb-6">
                  Tell Us About Your Business
                </Typography>
                
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-consciousness mb-2">
                        Full Name *
                      </label>
                      <input
                        type="text"
                        required
                        value={formData.fullName}
                        onChange={(e) => handleInputChange('fullName', e.target.value)}
                        className="w-full px-4 py-3 bg-abyssal-base border border-consciousness/30 rounded-[12px] text-white placeholder-gray-400 focus:border-consciousness/60 focus:outline-none transition-colors"
                        placeholder="Your full name"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-consciousness mb-2">
                        Business Email *
                      </label>
                      <input
                        type="email"
                        required
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        className="w-full px-4 py-3 bg-abyssal-base border border-consciousness/30 rounded-[12px] text-white placeholder-gray-400 focus:border-consciousness/60 focus:outline-none transition-colors"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-consciousness mb-2">
                        Company Name *
                      </label>
                      <input
                        type="text"
                        required
                        value={formData.company}
                        onChange={(e) => handleInputChange('company', e.target.value)}
                        className="w-full px-4 py-3 bg-abyssal-base border border-consciousness/30 rounded-[12px] text-white placeholder-gray-400 focus:border-consciousness/60 focus:outline-none transition-colors"
                        placeholder="Your company"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-consciousness mb-2">
                        Your Role *
                      </label>
                      <select
                        required
                        value={formData.role}
                        onChange={(e) => handleInputChange('role', e.target.value)}
                        className="w-full px-4 py-3 bg-abyssal-base border border-consciousness/30 rounded-[12px] text-white focus:border-consciousness/60 focus:outline-none transition-colors"
                      >
                        <option value="">Select your role</option>
                        <option value="ceo">CEO/Founder</option>
                        <option value="cto">CTO/Technology Leader</option>
                        <option value="operations">Operations Manager</option>
                        <option value="it">IT Director</option>
                        <option value="other">Other</option>
                      </select>
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-consciousness mb-2">
                        Phone Number
                      </label>
                      <input
                        type="tel"
                        value={formData.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        className="w-full px-4 py-3 bg-abyssal-base border border-consciousness/30 rounded-[12px] text-white placeholder-gray-400 focus:border-consciousness/60 focus:outline-none transition-colors"
                        placeholder="+****************"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-consciousness mb-2">
                        Company Size
                      </label>
                      <select
                        value={formData.employees}
                        onChange={(e) => handleInputChange('employees', e.target.value)}
                        className="w-full px-4 py-3 bg-abyssal-base border border-consciousness/30 rounded-[12px] text-white focus:border-consciousness/60 focus:outline-none transition-colors"
                      >
                        <option value="">Select size</option>
                        <option value="1-10">1-10 employees</option>
                        <option value="11-50">11-50 employees</option>
                        <option value="51-200">51-200 employees</option>
                        <option value="201-1000">201-1000 employees</option>
                        <option value="1000+">1000+ employees</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-consciousness mb-2">
                      Industry *
                    </label>
                    <select
                      required
                      value={formData.industry}
                      onChange={(e) => handleInputChange('industry', e.target.value)}
                      className="w-full px-4 py-3 bg-abyssal-base border border-consciousness/30 rounded-[12px] text-white focus:border-consciousness/60 focus:outline-none transition-colors"
                    >
                      <option value="">Select your industry</option>
                      <option value="manufacturing">Manufacturing</option>
                      <option value="logistics">Logistics & Supply Chain</option>
                      <option value="healthcare">Healthcare</option>
                      <option value="finance">Financial Services</option>
                      <option value="retail">Retail & E-commerce</option>
                      <option value="energy">Energy & Utilities</option>
                      <option value="agriculture">Agriculture</option>
                      <option value="other">Other</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-consciousness mb-2">
                      Current Challenges
                    </label>
                    <textarea
                      value={formData.challenges}
                      onChange={(e) => handleInputChange('challenges', e.target.value)}
                      rows={4}
                      className="w-full px-4 py-3 bg-abyssal-base border border-consciousness/30 rounded-[12px] text-white placeholder-gray-400 focus:border-consciousness/60 focus:outline-none transition-colors resize-none"
                      placeholder="Describe your current operational challenges or automation goals..."
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-consciousness mb-2">
                      Implementation Timeline
                    </label>
                    <select
                      value={formData.timeline}
                      onChange={(e) => handleInputChange('timeline', e.target.value)}
                      className="w-full px-4 py-3 bg-abyssal-base border border-consciousness/30 rounded-[12px] text-white focus:border-consciousness/60 focus:outline-none transition-colors"
                    >
                      <option value="">Select timeline</option>
                      <option value="immediate">Immediate (within 1 month)</option>
                      <option value="short">Short-term (1-3 months)</option>
                      <option value="medium">Medium-term (3-6 months)</option>
                      <option value="long">Long-term (6+ months)</option>
                      <option value="exploring">Just exploring</option>
                    </select>
                  </div>

                  <SentientButton
                    type="submit"
                    variant="quantum"
                    size="lg"
                    glow="intense"
                    className="w-full"
                  >
                    Request Personalized Demo
                    <ArrowRight className="w-5 h-5 ml-2" />
                  </SentientButton>
                </form>
              </Card>

              {/* Benefits */}
              <div className="space-y-8">
                <Card variant="neural" className="p-6 border-harmony/20">
                  <div className="flex items-start space-x-4">
                    <Calendar className="w-8 h-8 text-harmony mt-1" />
                    <div>
                      <Typography variant="lg" weight="semibold" color="harmony" className="mb-2">
                        30-Minute Personalized Session
                      </Typography>
                      <Typography variant="sm" color="secondary">
                        A dedicated session tailored to your specific industry and use cases, demonstrating real ROI potential.
                      </Typography>
                    </div>
                  </div>
                </Card>

                <Card variant="neural" className="p-6 border-consciousness/20">
                  <div className="flex items-start space-x-4">
                    <Users className="w-8 h-8 text-consciousness mt-1" />
                    <div>
                      <Typography variant="lg" weight="semibold" color="consciousness" className="mb-2">
                        Expert Consultation
                      </Typography>
                      <Typography variant="sm" color="secondary">
                        Meet with our ACI specialists who understand your industry's unique challenges and opportunities.
                      </Typography>
                    </div>
                  </div>
                </Card>

                <Card variant="neural" className="p-6 border-creativity/20">
                  <div className="flex items-start space-x-4">
                    <Zap className="w-8 h-8 text-creativity mt-1" />
                    <div>
                      <Typography variant="lg" weight="semibold" color="creativity" className="mb-2">
                        Live ROI Calculator
                      </Typography>
                      <Typography variant="sm" color="secondary">
                        See real-time calculations of potential cost savings and efficiency gains for your specific operations.
                      </Typography>
                    </div>
                  </div>
                </Card>

                <Card variant="neural" className="p-6 border-harmony/20">
                  <div className="flex items-start space-x-4">
                    <Shield className="w-8 h-8 text-harmony mt-1" />
                    <div>
                      <Typography variant="lg" weight="semibold" color="harmony" className="mb-2">
                        Security & Compliance Review
                      </Typography>
                      <Typography variant="sm" color="secondary">
                        Understand how SymbioAutomate meets your industry's security and regulatory requirements.
                      </Typography>
                    </div>
                  </div>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default RequestDemo;
