
import React from 'react';
import { cn } from '@/lib/utils';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'quantum' | 'neural' | 'cellular' | 'secondary' | 'tertiary' | 'outline-quantum';
  size?: 'sm' | 'md' | 'lg';
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  isLoading?: boolean;
  children: React.ReactNode;
}

const Button: React.FC<ButtonProps> = ({
  variant = 'quantum',
  size = 'md',
  leftIcon,
  rightIcon,
  isLoading = false,
  children,
  className,
  disabled,
  ...props
}) => {
  const getVariantStyles = () => {
    switch (variant) {
      case 'neural':
        return 'btn-neural';
      case 'cellular':
        return 'bg-harmony/10 border-harmony/30 hover:bg-harmony/20 hover:border-harmony/50 hover:shadow-[0_0_20px_rgba(0,255,119,0.3)]';
      case 'secondary':
        return 'bg-creativity/10 border-creativity/30 hover:bg-creativity/20 hover:border-creativity/50 hover:shadow-[0_0_20px_rgba(0,153,255,0.3)]';
      case 'tertiary':
        return 'bg-intuition/10 border-intuition/30 hover:bg-intuition/20 hover:border-intuition/50 hover:shadow-[0_0_20px_rgba(170,0,255,0.3)]';
      case 'outline-quantum':
        return 'bg-transparent border border-consciousness/70 text-consciousness hover:bg-consciousness/10 hover:border-consciousness rounded-lg transition-all duration-quantum ease-quantum';
      default:
        return 'btn-quantum';
    }
  };

  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return 'px-3 py-2 text-sm';
      case 'lg':
        return 'px-8 py-4 text-lg';
      default:
        return 'px-5 py-3 text-base';
    }
  };

  const baseStyles = 'relative inline-flex items-center justify-center font-medium transition-all duration-quantum ease-quantum cursor-pointer overflow-hidden rounded-lg border';

  return (
    <button
      className={cn(
        baseStyles,
        getVariantStyles(),
        getSizeStyles(),
        isLoading && 'opacity-75 cursor-not-allowed pointer-events-none',
        disabled && 'opacity-50 cursor-not-allowed pointer-events-none',
        className
      )}
      disabled={disabled || isLoading}
      {...props}
    >
      {isLoading ? (
        <div className="w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin" />
      ) : (
        <>
          {leftIcon && <span className="mr-2 flex items-center">{leftIcon}</span>}
          <span className="relative z-10">{children}</span>
          {rightIcon && <span className="ml-2 flex items-center">{rightIcon}</span>}
        </>
      )}
    </button>
  );
};

export default Button;
