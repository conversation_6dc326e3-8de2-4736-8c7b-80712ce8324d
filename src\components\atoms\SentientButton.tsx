import React, { useState, useRef, useCallback, useEffect } from 'react';

// Utility function for className merging
const cn = (...classes: (string | undefined | null | boolean)[]) => {
  return classes.filter(Boolean).join(' ');
};

// Bio-Quantum Button variant configurations
const sentientButtonVariants = {
  base: [
    "relative inline-flex items-center justify-center",
    "font-semibold text-sm leading-none tracking-wide",
    "border transition-all duration-300 ease-out",
    "focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-abyssal-void",
    "disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none",
    "select-none overflow-hidden group cursor-pointer",
    "font-quantum backdrop-blur-sm"
  ].join(' '),

  variants: {
    quantum: [
      "bg-gradient-to-br from-consciousness-500 via-consciousness-400 to-consciousness-secondary",
      "border-consciousness-500 text-abyssal-void shadow-quantum-primary",
      "hover:from-consciousness-400 hover:via-consciousness-300 hover:to-consciousness-secondary",
      "hover:border-consciousness-400 hover:shadow-quantum-intense hover:-translate-y-1 hover:scale-105",
      "active:from-consciousness-600 active:to-consciousness-700 active:translate-y-0 active:scale-100",
      "focus:ring-consciousness-400",
      "rounded-neural"
    ].join(' '),

    morphic: [
      "bg-gradient-to-br from-harmony-500 via-harmony-400 to-symbioautomate-500",
      "border-harmony-500 text-abyssal-void shadow-division-auto",
      "hover:from-harmony-400 hover:via-harmony-300 hover:to-symbioautomate-400",
      "hover:border-harmony-400 hover:shadow-[0_0_40px_rgba(0,255,127,0.6)] hover:-translate-y-1 hover:scale-105",
      "active:from-harmony-600 active:to-symbioautomate-600 active:translate-y-0 active:scale-100",
      "focus:ring-harmony-400",
      "rounded-cellular"
    ].join(' '),

    neural: [
      "bg-gradient-to-br from-creativity-500 via-creativity-400 to-symbiolabs-500",
      "border-creativity-500 text-abyssal-void shadow-division-labs",
      "hover:from-creativity-400 hover:via-creativity-300 hover:to-symbiolabs-400",
      "hover:border-creativity-400 hover:shadow-[0_0_40px_rgba(255,215,0,0.6)] hover:-translate-y-1 hover:scale-105",
      "active:from-creativity-600 active:to-symbiolabs-600 active:translate-y-0 active:scale-100",
      "focus:ring-creativity-400",
      "rounded-quantum"
    ].join(' '),

    ghost: [
      "bg-transparent border-consciousness-500/30 text-consciousness-400",
      "hover:bg-consciousness-500/10 hover:border-consciousness-400/50 hover:text-consciousness-300",
      "hover:shadow-sentient-glow hover:-translate-y-0.5",
      "active:bg-consciousness-500/20 active:translate-y-0",
      "focus:ring-consciousness-400",
      "rounded-biomorphic"
    ].join(' '),

    outline: [
      "bg-abyssal-elevated/50 border-consciousness-500/50 text-consciousness-400 backdrop-blur-md",
      "hover:bg-consciousness-500/10 hover:border-consciousness-400 hover:text-consciousness-300",
      "hover:shadow-sentient-glow hover:-translate-y-0.5",
      "active:bg-consciousness-500/20 active:translate-y-0",
      "focus:ring-consciousness-400",
      "rounded-organic"
    ].join(' '),

    transcendent: [
      "bg-gradient-to-br from-intuition-500 via-symbioxchange-500 to-transcendence-500",
      "border-intuition-500 text-text-primary shadow-holographic",
      "hover:from-intuition-400 hover:via-symbioxchange-400 hover:to-transcendence-400",
      "hover:border-intuition-400 hover:shadow-[0_0_50px_rgba(199,0,255,0.8)] hover:-translate-y-1 hover:scale-105",
      "active:from-intuition-600 active:to-transcendence-600 active:translate-y-0 active:scale-100",
      "focus:ring-intuition-400",
      "rounded-flow"
    ].join(' ')
  },

  sizes: {
    sm: "px-4 py-2 text-xs min-h-[36px] gap-2",
    md: "px-6 py-3 text-sm min-h-[44px] gap-2",
    lg: "px-8 py-4 text-base min-h-[52px] gap-3",
    xl: "px-10 py-5 text-lg min-h-[60px] gap-3"
  },

  widths: {
    auto: "w-auto",
    full: "w-full",
    fit: "w-fit"
  },

  glow: {
    subtle: "hover:shadow-sentient-glow",
    medium: "hover:shadow-quantum-primary",
    intense: "hover:shadow-quantum-intense hover:shadow-holographic",
    transcendent: "hover:shadow-[0_0_60px_rgba(0,229,255,0.8),0_0_120px_rgba(0,255,194,0.4)]"
  }
};

// Helper function to get button classes
const getSentientButtonClasses = (
  variant: keyof typeof sentientButtonVariants.variants = 'quantum',
  size: keyof typeof sentientButtonVariants.sizes = 'md',
  width: keyof typeof sentientButtonVariants.widths = 'auto',
  glow: keyof typeof sentientButtonVariants.glow = 'medium'
) => {
  return cn(
    sentientButtonVariants.base,
    sentientButtonVariants.variants[variant],
    sentientButtonVariants.sizes[size],
    sentientButtonVariants.widths[width],
    sentientButtonVariants.glow[glow]
  );
};

interface SentientButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  variant?: keyof typeof sentientButtonVariants.variants;
  size?: keyof typeof sentientButtonVariants.sizes;
  width?: keyof typeof sentientButtonVariants.widths;
  glow?: keyof typeof sentientButtonVariants.glow;
  loading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  enableQuantumRipple?: boolean;
  enableMorphicShift?: boolean;
}

const SentientButton: React.FC<SentientButtonProps> = ({
  className,
  variant = 'quantum',
  size = 'md',
  width = 'auto',
  glow = 'medium',
  children,
  loading = false,
  leftIcon,
  rightIcon,
  disabled,
  onClick,
  enableQuantumRipple = true,
  enableMorphicShift = true,
  ...props
}) => {
  const [isPressed, setIsPressed] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [quantumParticles, setQuantumParticles] = useState<Array<{id: number, x: number, y: number, life: number}>>([]);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const particleId = useRef(0);

  // Quantum particle animation
  useEffect(() => {
    if (!isHovered) return;

    const interval = setInterval(() => {
      setQuantumParticles(prev => {
        const updated = prev
          .map(p => ({ ...p, life: p.life - 0.05 }))
          .filter(p => p.life > 0);

        if (Math.random() > 0.7 && updated.length < 8) {
          updated.push({
            id: particleId.current++,
            x: Math.random() * 100,
            y: Math.random() * 100,
            life: 1
          });
        }

        return updated;
      });
    }, 100);

    return () => clearInterval(interval);
  }, [isHovered]);

  const createQuantumRipple = useCallback((event: React.MouseEvent<HTMLButtonElement>) => {
    if (!enableQuantumRipple) return;

    const button = buttonRef.current;
    if (!button) return;

    const rect = button.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height) * 1.5;
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;

    // Create multiple ripple layers for quantum effect
    for (let i = 0; i < 3; i++) {
      setTimeout(() => {
        const ripple = document.createElement('div');
        ripple.className = 'absolute rounded-full pointer-events-none';
        ripple.style.cssText = `
          width: ${size}px;
          height: ${size}px;
          left: ${x}px;
          top: ${y}px;
          background: radial-gradient(circle, rgba(0,229,255,${0.3 - i * 0.1}) 0%, transparent 70%);
          animation: quantumRipple ${0.8 + i * 0.2}s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          border: 1px solid rgba(0,229,255,${0.5 - i * 0.15});
        `;

        button.appendChild(ripple);
        setTimeout(() => ripple.remove(), 1200);
      }, i * 100);
    }
  }, [enableQuantumRipple]);

  const handleClick = useCallback((event: React.MouseEvent<HTMLButtonElement>) => {
    if (disabled || loading) return;

    createQuantumRipple(event);
    onClick?.(event);
  }, [disabled, loading, onClick, createQuantumRipple]);

  const isDisabled = disabled || loading;

  return (
    <>
      <style>{`
        @keyframes quantumRipple {
          0% {
            transform: scale(0);
            opacity: 1;
          }
          100% {
            transform: scale(2.5);
            opacity: 0;
          }
        }

        @keyframes quantumSpin {
          from {
            transform: rotate(0deg);
          }
          to {
            transform: rotate(360deg);
          }
        }

        @keyframes morphicShift {
          0%, 100% {
            border-radius: 48% 52% 68% 32% / 42% 61% 39% 58%;
          }
          25% {
            border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
          }
          50% {
            border-radius: 63% 37% 54% 46% / 55% 48% 52% 45%;
          }
          75% {
            border-radius: 51% 49% 48% 52% / 62% 44% 56% 38%;
          }
        }

        .quantum-spinner {
          animation: quantumSpin 1.2s linear infinite;
        }

        .morphic-shift {
          animation: morphicShift 4s ease-in-out infinite;
        }
      `}</style>

      <button
        ref={buttonRef}
        className={cn(
          getSentientButtonClasses(variant, size, width, glow),
          enableMorphicShift && 'morphic-shift',
          className
        )}
        disabled={isDisabled}
        onClick={handleClick}
        onMouseDown={() => setIsPressed(true)}
        onMouseUp={() => setIsPressed(false)}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => {
          setIsPressed(false);
          setIsHovered(false);
        }}
        {...props}
      >
        {/* Quantum Field Background */}
        <div className="absolute inset-0 opacity-30">
          {quantumParticles.map(particle => (
            <div
              key={particle.id}
              className="absolute w-1 h-1 bg-consciousness-secondary rounded-full animate-pulse"
              style={{
                left: `${particle.x}%`,
                top: `${particle.y}%`,
                opacity: particle.life,
                transform: `scale(${particle.life})`,
                filter: `blur(${(1 - particle.life) * 2}px)`
              }}
            />
          ))}
        </div>

        {/* Bio-Quantum Shine Effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-consciousness-secondary/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out" />

        {/* Neural Network Pattern Overlay */}
        <div className="absolute inset-0 opacity-20 group-hover:opacity-40 transition-opacity duration-300">
          <div className="absolute top-1/4 left-1/4 w-px h-1/2 bg-consciousness-secondary/50 rotate-45"></div>
          <div className="absolute top-1/4 right-1/4 w-px h-1/2 bg-consciousness-secondary/50 -rotate-45"></div>
          <div className="absolute top-1/2 left-1/2 w-2 h-2 bg-consciousness-secondary/60 rounded-full transform -translate-x-1/2 -translate-y-1/2"></div>
        </div>

        {/* Content */}
        <span className="relative flex items-center justify-center z-10">
          {loading ? (
            <svg
              className="quantum-spinner w-5 h-5"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="3"
              />
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              />
              <circle
                className="opacity-50"
                cx="12"
                cy="12"
                r="6"
                stroke="currentColor"
                strokeWidth="2"
                fill="none"
              />
            </svg>
          ) : leftIcon ? (
            <span className="flex-shrink-0 transition-transform duration-200 group-hover:scale-110">{leftIcon}</span>
          ) : null}

          <span className={cn(
            "font-quantum transition-all duration-200",
            loading ? "opacity-70" : "group-hover:tracking-wider"
          )}>
            {loading ? "Processing..." : children}
          </span>

          {!loading && rightIcon && (
            <span className="flex-shrink-0 transition-transform duration-200 group-hover:scale-110 group-hover:translate-x-1">{rightIcon}</span>
          )}
        </span>

        {/* Quantum Glow Pulse */}
        <div className="absolute inset-0 rounded-inherit opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none">
          <div className="absolute inset-0 rounded-inherit bg-gradient-to-br from-consciousness-400/20 to-consciousness-secondary/20 animate-pulse"></div>
        </div>
      </button>
    </>
  );
};

export default SentientButton;
