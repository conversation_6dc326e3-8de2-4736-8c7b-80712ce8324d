
import React, { useState, useRef } from 'react';
import { cn } from '@/lib/utils';
import { cva, type VariantProps } from 'class-variance-authority';

const sentientButtonVariants = cva(
  "relative inline-flex items-center justify-center font-medium transition-all duration-medium ease-morphic cursor-pointer overflow-hidden user-select-none outline-none",
  {
    variants: {
      variant: {
        quantum: [
          "bg-gradient-to-br from-consciousness-500 to-consciousness-secondary",
          "border border-consciousness-500",
          "text-abyssal-void",
          "rounded-neural",
          "hover:rounded-quantum",
          "hover:shadow-quantum-intense",
          "hover:scale-105 hover:-translate-y-1",
          "active:scale-102 active:translate-y-0",
          "focus-visible:ring-2 focus-visible:ring-consciousness-500 focus-visible:ring-offset-2 focus-visible:ring-offset-abyssal-base"
        ],
        division: [
          "bg-gradient-to-br from-symbioautomate-500 to-symbioautomate-600",
          "border border-symbioautomate-500",
          "text-abyssal-void",
          "rounded-organic",
          "hover:rounded-cellular",
          "hover:shadow-division-auto",
          "hover:scale-105 hover:-translate-y-1",
          "active:scale-102 active:translate-y-0"
        ],
        ghost: [
          "bg-transparent",
          "border border-text-tertiary/30",
          "text-text-primary",
          "rounded-organic",
          "hover:bg-consciousness-500/10",
          "hover:border-consciousness-500/50",
          "hover:text-consciousness-500",
          "hover:shadow-sentient-glow"
        ],
        morphic: [
          "bg-gradient-to-br from-text-primary/8 to-text-secondary/5",
          "border border-text-tertiary/20",
          "text-text-primary",
          "rounded-biomorphic",
          "hover:rounded-flow",
          "backdrop-blur-xl",
          "hover:bg-gradient-to-br hover:from-consciousness-500/15 hover:to-consciousness-secondary/10",
          "hover:border-consciousness-500/30"
        ]
      },
      size: {
        sm: "px-4 py-2 text-sm",
        md: "px-6 py-3 text-base",
        lg: "px-8 py-4 text-lg",
        xl: "px-10 py-5 text-xl",
      },
      glow: {
        none: "",
        subtle: "hover:animate-anticipatory-glow",
        intense: "animate-sentient-pulse",
      }
    },
    defaultVariants: {
      variant: "quantum",
      size: "md",
      glow: "subtle",
    },
  }
);

interface SentientButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof sentientButtonVariants> {
  children: React.ReactNode;
  division?: 'symbioautomate' | 'symbiolabs' | 'symbioxchange' | 'symbioedge' | 'symbioimpact' | 'symbioventures' | 'symbioalliance';
}

const SentientButton: React.FC<SentientButtonProps> = ({
  className,
  variant,
  size,
  glow,
  division,
  children,
  onMouseEnter,
  onMouseLeave,
  onClick,
  ...props
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isPressed, setIsPressed] = useState(false);
  const buttonRef = useRef<HTMLButtonElement>(null);

  const handleMouseEnter = (e: React.MouseEvent<HTMLButtonElement>) => {
    setIsHovered(true);
    onMouseEnter?.(e);
  };

  const handleMouseLeave = (e: React.MouseEvent<HTMLButtonElement>) => {
    setIsHovered(false);
    onMouseLeave?.(e);
  };

  const handleMouseDown = () => {
    setIsPressed(true);
  };

  const handleMouseUp = () => {
    setIsPressed(false);
  };

  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    // Quantum ripple effect
    if (buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      const ripple = document.createElement('div');
      const size = Math.max(rect.width, rect.height);
      const x = e.clientX - rect.left - size / 2;
      const y = e.clientY - rect.top - size / 2;
      
      ripple.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        left: ${x}px;
        top: ${y}px;
        background: radial-gradient(circle, rgba(255,255,255,0.6) 0%, transparent 70%);
        border-radius: 50%;
        transform: scale(0);
        animation: quantum-ripple 600ms ease-out;
        pointer-events: none;
        z-index: 10;
      `;
      
      buttonRef.current.appendChild(ripple);
      
      setTimeout(() => {
        ripple.remove();
      }, 600);
    }
    
    onClick?.(e);
  };

  // Division-specific styling
  const divisionClasses = division ? {
    symbioautomate: "bg-gradient-to-br from-symbioautomate-500 to-symbioautomate-600 border-symbioautomate-500 hover:shadow-division-auto",
    symbiolabs: "bg-gradient-to-br from-symbiolabs-500 to-symbiolabs-600 border-symbiolabs-500 hover:shadow-division-labs",
    symbioxchange: "bg-gradient-to-br from-symbioxchange-500 to-symbioxchange-600 border-symbioxchange-500",
    symbioedge: "bg-gradient-to-br from-symbioedge-500 to-symbioedge-600 border-symbioedge-500",
    symbioimpact: "bg-gradient-to-br from-symbioimpact-500 to-symbioimpact-600 border-symbioimpact-500",
    symbioventures: "bg-gradient-to-br from-symbioventures-500 to-symbioventures-600 border-symbioventures-500",
    symbioalliance: "bg-gradient-to-br from-symbioalliance-500 to-symbioalliance-600 border-symbioalliance-500",
  }[division] : "";

  return (
    <>
      <style>{`
        @keyframes quantum-ripple {
          0% {
            transform: scale(0);
            opacity: 1;
          }
          100% {
            transform: scale(2);
            opacity: 0;
          }
        }
      `}</style>
      
      <button
        ref={buttonRef}
        className={cn(
          sentientButtonVariants({ variant, size, glow }),
          division && divisionClasses,
          className
        )}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onMouseDown={handleMouseDown}
        onMouseUp={handleMouseUp}
        onClick={handleClick}
        {...props}
      >
        {/* Quantum light sweep effect */}
        <div
          className={cn(
            "absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent",
            "transform -translate-x-full transition-transform duration-slow ease-organic",
            isHovered && "translate-x-full"
          )}
        />
        
        {/* Anticipatory glow overlay */}
        <div
          className={cn(
            "absolute inset-0 rounded-inherit",
            "bg-gradient-to-br from-consciousness-500/20 to-consciousness-secondary/10",
            "opacity-0 transition-opacity duration-fast ease-sentient",
            isHovered && "opacity-100"
          )}
        />
        
        {/* Content */}
        <span className="relative z-10 flex items-center gap-2 font-precision-body">
          {children}
        </span>
        
        {/* Morphic pulse indicator */}
        {glow === "intense" && (
          <div className="absolute -inset-1 rounded-inherit bg-consciousness-500/20 animate-morphic-flow opacity-50" />
        )}
      </button>
    </>
  );
};

export default SentientButton;
