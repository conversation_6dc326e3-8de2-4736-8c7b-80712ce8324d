import React, { useState, useRef, useCallback } from 'react';

// Utility function for className merging
const cn = (...classes: (string | undefined | null | boolean)[]) => {
  return classes.filter(Boolean).join(' ');
};

// Button variant configurations
const buttonVariants = {
  base: [
    "relative inline-flex items-center justify-center",
    "font-semibold text-sm leading-none tracking-wide",
    "border transition-all duration-200 ease-out",
    "focus:outline-none focus:ring-2 focus:ring-offset-2",
    "disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none",
    "select-none overflow-hidden group"
  ].join(' '),
  
  variants: {
    primary: [
      "bg-gradient-to-b from-blue-600 to-blue-700",
      "border-blue-600 text-white shadow-sm",
      "hover:from-blue-700 hover:to-blue-800 hover:border-blue-700",
      "hover:shadow-md hover:-translate-y-0.5",
      "active:from-blue-800 active:to-blue-900 active:translate-y-0",
      "focus:ring-blue-500"
    ].join(' '),
    
    secondary: [
      "bg-white border-gray-300 text-gray-900 shadow-sm",
      "hover:bg-gray-50 hover:border-gray-400 hover:shadow-md hover:-translate-y-0.5",
      "active:bg-gray-100 active:translate-y-0",
      "focus:ring-gray-500"
    ].join(' '),
    
    outline: [
      "bg-transparent border-gray-300 text-gray-700",
      "hover:bg-gray-50 hover:border-gray-400 hover:text-gray-900",
      "hover:shadow-sm hover:-translate-y-0.5",
      "active:bg-gray-100 active:translate-y-0",
      "focus:ring-gray-500"
    ].join(' '),
    
    ghost: [
      "bg-transparent border-transparent text-gray-700",
      "hover:bg-gray-100 hover:text-gray-900",
      "active:bg-gray-200",
      "focus:ring-gray-500"
    ].join(' '),
    
    success: [
      "bg-gradient-to-b from-emerald-600 to-emerald-700",
      "border-emerald-600 text-white shadow-sm",
      "hover:from-emerald-700 hover:to-emerald-800 hover:border-emerald-700",
      "hover:shadow-md hover:-translate-y-0.5",
      "active:from-emerald-800 active:to-emerald-900 active:translate-y-0",
      "focus:ring-emerald-500"
    ].join(' '),
    
    danger: [
      "bg-gradient-to-b from-red-600 to-red-700",
      "border-red-600 text-white shadow-sm",
      "hover:from-red-700 hover:to-red-800 hover:border-red-700",
      "hover:shadow-md hover:-translate-y-0.5",
      "active:from-red-800 active:to-red-900 active:translate-y-0",
      "focus:ring-red-500"
    ].join(' ')
  },
  
  sizes: {
    sm: "px-3 py-2 text-xs rounded-md min-h-[32px]",
    md: "px-4 py-2.5 text-sm rounded-lg min-h-[40px]",
    lg: "px-6 py-3 text-base rounded-lg min-h-[48px]",
    xl: "px-8 py-4 text-lg rounded-xl min-h-[56px]"
  },
  
  widths: {
    auto: "w-auto",
    full: "w-full",
    fit: "w-fit"
  }
};

// Helper function to get button classes
const getButtonClasses = (
  variant: keyof typeof buttonVariants.variants = 'primary',
  size: keyof typeof buttonVariants.sizes = 'md',
  width: keyof typeof buttonVariants.widths = 'auto'
) => {
  return cn(
    buttonVariants.base,
    buttonVariants.variants[variant],
    buttonVariants.sizes[size],
    buttonVariants.widths[width]
  );
};

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  variant?: keyof typeof buttonVariants.variants;
  size?: keyof typeof buttonVariants.sizes;
  width?: keyof typeof buttonVariants.widths;
  loading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

const ProfessionalButton: React.FC<ButtonProps> = ({
  className,
  variant,
  size,
  width,
  children,
  loading = false,
  leftIcon,
  rightIcon,
  disabled,
  onClick,
  ...props
}) => {
  const [isPressed, setIsPressed] = useState(false);
  const buttonRef = useRef<HTMLButtonElement>(null);

  const createRipple = useCallback((event: React.MouseEvent<HTMLButtonElement>) => {
    const button = buttonRef.current;
    if (!button) return;

    const rect = button.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;

    const ripple = document.createElement('div');
    ripple.className = 'absolute rounded-full bg-white/30 pointer-events-none animate-ping';
    ripple.style.cssText = `
      width: ${size}px;
      height: ${size}px;
      left: ${x}px;
      top: ${y}px;
      animation: ripple 0.6s ease-out;
    `;

    button.appendChild(ripple);
    setTimeout(() => ripple.remove(), 600);
  }, []);

  const handleClick = useCallback((event: React.MouseEvent<HTMLButtonElement>) => {
    if (disabled || loading) return;
    
    createRipple(event);
    onClick?.(event);
  }, [disabled, loading, onClick, createRipple]);

  const isDisabled = disabled || loading;

  return (
    <>
      <style>{`
        @keyframes ripple {
          0% {
            transform: scale(0);
            opacity: 1;
          }
          100% {
            transform: scale(2);
            opacity: 0;
          }
        }
        
        @keyframes spin {
          from {
            transform: rotate(0deg);
          }
          to {
            transform: rotate(360deg);
          }
        }
        
        .loading-spinner {
          animation: spin 1s linear infinite;
        }
      `}</style>
      
      <button
        ref={buttonRef}
        className={cn(getButtonClasses(variant, size, width), className)}
        disabled={isDisabled}
        onClick={handleClick}
        onMouseDown={() => setIsPressed(true)}
        onMouseUp={() => setIsPressed(false)}
        onMouseLeave={() => setIsPressed(false)}
        {...props}
      >
        {/* Shine effect on hover */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700 ease-out" />
        
        {/* Content */}
        <span className="relative flex items-center justify-center gap-2">
          {loading ? (
            <svg
              className="loading-spinner w-4 h-4"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              />
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              />
            </svg>
          ) : leftIcon ? (
            <span className="flex-shrink-0">{leftIcon}</span>
          ) : null}
          
          <span className={loading ? "opacity-70" : ""}>
            {loading ? "Loading..." : children}
          </span>
          
          {!loading && rightIcon && (
            <span className="flex-shrink-0">{rightIcon}</span>
          )}
        </span>
      </button>
    </>
  );
};

export default SentientButton;
