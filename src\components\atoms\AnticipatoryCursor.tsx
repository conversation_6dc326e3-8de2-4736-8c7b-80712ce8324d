import React, { useState, useEffect, useRef, useCallback } from 'react';

interface AnticipatoryCursorProps {
  children: React.ReactNode;
  className?: string;
  glowColor?: 'consciousness' | 'harmony' | 'creativity' | 'intuition' | 'platinum' | 'aurora';
  intensity?: 'subtle' | 'medium' | 'intense' | 'enterprise';
  rippleCount?: number;
  enableParticles?: boolean;
}

const AnticipatoryCursor: React.FC<AnticipatoryCursorProps> = ({
  children,
  className = '',
  glowColor = 'platinum',
  intensity = 'enterprise',
  rippleCount = 3,
  enableParticles = true
}) => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovered, setIsHovered] = useState(false);
  const [anticipation, setAnticipation] = useState(0);
  const [velocity, setVelocity] = useState({ x: 0, y: 0 });
  const [particles, setParticles] = useState<Array<{id: number, x: number, y: number, life: number}>>([]);
  const lastMousePosition = useRef({ x: 0, y: 0 });
  const animationFrame = useRef<number>();
  const particleId = useRef(0);

  const colorSchemes = {
    consciousness: {
      primary: 'rgb(99, 102, 241)',
      secondary: 'rgb(139, 92, 246)',
      accent: 'rgb(168, 85, 247)',
      glow: 'rgba(99, 102, 241, 0.4)'
    },
    harmony: {
      primary: 'rgb(34, 197, 94)',
      secondary: 'rgb(16, 185, 129)',
      accent: 'rgb(6, 182, 212)',
      glow: 'rgba(34, 197, 94, 0.4)'
    },
    creativity: {
      primary: 'rgb(251, 146, 60)',
      secondary: 'rgb(245, 101, 101)',
      accent: 'rgb(244, 63, 94)',
      glow: 'rgba(251, 146, 60, 0.4)'
    },
    intuition: {
      primary: 'rgb(168, 85, 247)',
      secondary: 'rgb(217, 70, 239)',
      accent: 'rgb(236, 72, 153)',
      glow: 'rgba(168, 85, 247, 0.4)'
    },
    platinum: {
      primary: 'rgb(148, 163, 184)',
      secondary: 'rgb(203, 213, 225)',
      accent: 'rgb(226, 232, 240)',
      glow: 'rgba(148, 163, 184, 0.3)'
    },
    aurora: {
      primary: 'rgb(34, 211, 238)',
      secondary: 'rgb(168, 85, 247)',
      accent: 'rgb(251, 146, 60)',
      glow: 'rgba(34, 211, 238, 0.4)'
    }
  };

  const getIntensityConfig = () => {
    switch (intensity) {
      case 'subtle': return { glow: 20, ripple: 30, particles: 2 };
      case 'medium': return { glow: 35, ripple: 50, particles: 4 };
      case 'intense': return { glow: 55, ripple: 80, particles: 6 };
      case 'enterprise': return { glow: 45, ripple: 70, particles: 5 };
      default: return { glow: 45, ripple: 70, particles: 5 };
    }
  };

  const config = getIntensityConfig();
  const colors = colorSchemes[glowColor] || colorSchemes.platinum;

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const newPosition = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    };

    // Calculate velocity for particle effects
    const newVelocity = {
      x: newPosition.x - lastMousePosition.current.x,
      y: newPosition.y - lastMousePosition.current.y
    };

    setMousePosition(newPosition);
    setVelocity(newVelocity);
    lastMousePosition.current = newPosition;

    // Generate particles based on movement speed
    if (enableParticles && isHovered) {
      const speed = Math.sqrt(newVelocity.x ** 2 + newVelocity.y ** 2);
      if (speed > 5 && Math.random() > 0.7) {
        setParticles(prev => [...prev.slice(-config.particles), {
          id: particleId.current++,
          x: newPosition.x + (Math.random() - 0.5) * 20,
          y: newPosition.y + (Math.random() - 0.5) * 20,
          life: 1
        }]);
      }
    }
  }, [isHovered, enableParticles, config.particles]);

  useEffect(() => {
    const animate = () => {
      if (isHovered) {
        setAnticipation(prev => Math.min(prev + 0.03, 1));
      } else {
        setAnticipation(prev => Math.max(prev - 0.05, 0));
      }

      // Update particles
      setParticles(prev => prev
        .map(particle => ({ ...particle, life: particle.life - 0.02 }))
        .filter(particle => particle.life > 0)
      );

      animationFrame.current = requestAnimationFrame(animate);
    };

    animationFrame.current = requestAnimationFrame(animate);
    return () => {
      if (animationFrame.current) {
        cancelAnimationFrame(animationFrame.current);
      }
    };
  }, [isHovered]);

  const renderRipples = () => {
    return Array.from({ length: rippleCount }, (_, i) => (
      <div
        key={i}
        className="absolute pointer-events-none rounded-full border transition-all duration-700 ease-out"
        style={{
          left: mousePosition.x - config.ripple / 2,
          top: mousePosition.y - config.ripple / 2,
          width: config.ripple,
          height: config.ripple,
          borderColor: colors.primary,
          borderWidth: `${1 + i * 0.5}px`,
          opacity: isHovered ? (0.6 - i * 0.15) * anticipation : 0,
          transform: `scale(${isHovered ? 1 + i * 0.3 + anticipation * 0.5 : 0.3})`,
          transitionDelay: `${i * 100}ms`,
          filter: 'blur(0.5px)'
        }}
      />
    ));
  };

  const renderParticles = () => {
    if (!enableParticles) return null;
    
    return particles.map(particle => (
      <div
        key={particle.id}
        className="absolute pointer-events-none rounded-full"
        style={{
          left: particle.x - 2,
          top: particle.y - 2,
          width: 4,
          height: 4,
          background: `linear-gradient(45deg, ${colors.secondary}, ${colors.accent})`,
          opacity: particle.life * 0.8,
          transform: `scale(${particle.life})`,
          filter: 'blur(0.5px)',
          boxShadow: `0 0 ${particle.life * 10}px ${colors.glow}`
        }}
      />
    ));
  };

  return (
    <div
      className={`relative overflow-hidden cursor-none ${className}`}
      onMouseMove={handleMouseMove}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{ isolation: 'isolate' }}
    >
      {children}
      
      {/* Main Glow Field */}
      <div 
        className="absolute pointer-events-none transition-all duration-500 ease-out rounded-full"
        style={{
          left: mousePosition.x - config.glow,
          top: mousePosition.y - config.glow,
          width: config.glow * 2,
          height: config.glow * 2,
          background: `radial-gradient(circle, ${colors.glow} 0%, rgba(255,255,255,0.1) 40%, transparent 70%)`,
          opacity: isHovered ? anticipation : 0,
          transform: `scale(${1 + anticipation * 0.3})`,
          filter: 'blur(8px)',
          mixBlendMode: 'screen'
        }}
      />

      {/* Secondary Glow */}
      <div 
        className="absolute pointer-events-none transition-all duration-700 ease-out rounded-full"
        style={{
          left: mousePosition.x - config.glow * 0.6,
          top: mousePosition.y - config.glow * 0.6,
          width: config.glow * 1.2,
          height: config.glow * 1.2,
          background: `radial-gradient(circle, ${colors.primary}20 0%, transparent 60%)`,
          opacity: isHovered ? anticipation * 0.6 : 0,
          transform: `scale(${1 + anticipation * 0.2})`,
          filter: 'blur(4px)'
        }}
      />

      {/* Core Cursor */}
      <div 
        className="absolute pointer-events-none transition-all duration-300 ease-out rounded-full"
        style={{
          left: mousePosition.x - 6,
          top: mousePosition.y - 6,
          width: 12,
          height: 12,
          background: `linear-gradient(135deg, ${colors.primary}, ${colors.secondary})`,
          opacity: isHovered ? 1 : 0,
          transform: `scale(${1 + anticipation * 0.5})`,
          boxShadow: `0 0 ${anticipation * 20}px ${colors.glow}, inset 0 0 6px rgba(255,255,255,0.3)`,
          border: `1px solid rgba(255,255,255,0.2)`
        }}
      />

      {/* Ripple Effects */}
      {renderRipples()}

      {/* Particle System */}
      {renderParticles()}
      
      {/* Velocity Trail */}
      {Math.abs(velocity.x) + Math.abs(velocity.y) > 10 && (
        <div
          className="absolute pointer-events-none transition-all duration-200 ease-out"
          style={{
            left: mousePosition.x - Math.abs(velocity.x) * 0.5,
            top: mousePosition.y - Math.abs(velocity.y) * 0.5,
            width: Math.abs(velocity.x),
            height: Math.abs(velocity.y),
            background: `linear-gradient(${Math.atan2(velocity.y, velocity.x) * 180 / Math.PI}deg, transparent, ${colors.accent}40, transparent)`,
            opacity: isHovered ? anticipation * 0.4 : 0,
            filter: 'blur(2px)',
            borderRadius: '50%'
          }}
        />
      )}
    </div>
  );
};

export default AnticipatoryCursor;
