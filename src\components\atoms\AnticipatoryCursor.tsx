
import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface AnticipatoryCursorProps {
  children: React.ReactNode;
  className?: string;
  glowColor?: 'consciousness' | 'harmony' | 'creativity' | 'intuition';
  intensity?: 'subtle' | 'medium' | 'intense';
}

const AnticipatoryCursor: React.FC<AnticipatoryCursorProps> = ({
  children,
  className,
  glowColor = 'consciousness',
  intensity = 'medium'
}) => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovered, setIsHovered] = useState(false);
  const [anticipation, setAnticipation] = useState(0);

  useEffect(() => {
    let animationFrame: number;
    
    if (isHovered) {
      const animate = () => {
        setAnticipation(prev => Math.min(prev + 0.02, 1));
        animationFrame = requestAnimationFrame(animate);
      };
      animationFrame = requestAnimationFrame(animate);
    } else {
      setAnticipation(0);
    }

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [isHovered]);

  const handleMouseMove = (e: React.MouseEvent) => {
    const rect = e.currentTarget.getBoundingClientRect();
    setMousePosition({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    });
  };

  const getGlowIntensity = () => {
    switch (intensity) {
      case 'subtle': return 15;
      case 'medium': return 25;
      case 'intense': return 40;
      default: return 25;
    }
  };

  return (
    <div
      className={cn("relative overflow-hidden", className)}
      onMouseMove={handleMouseMove}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {children}
      
      {/* Anticipatory Glow Field */}
      <div 
        className={cn(
          "absolute pointer-events-none transition-opacity duration-300 rounded-full blur-xl",
          `bg-${glowColor}/20`,
          isHovered ? "opacity-100" : "opacity-0"
        )}
        style={{
          left: mousePosition.x - getGlowIntensity(),
          top: mousePosition.y - getGlowIntensity(),
          width: getGlowIntensity() * 2,
          height: getGlowIntensity() * 2,
          transform: `scale(${1 + anticipation * 0.5})`
        }}
      />
      
      {/* Quantum Ripple Effect */}
      <div 
        className={cn(
          "absolute pointer-events-none transition-all duration-500 rounded-full border",
          `border-${glowColor}/30`,
          isHovered ? "opacity-60 scale-150" : "opacity-0 scale-50"
        )}
        style={{
          left: mousePosition.x - 20,
          top: mousePosition.y - 20,
          width: 40,
          height: 40,
        }}
      />
    </div>
  );
};

export default AnticipatoryCursor;
