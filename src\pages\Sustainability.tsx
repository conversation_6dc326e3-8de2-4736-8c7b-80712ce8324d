
import React from 'react';
import Header from '../components/organisms/Header';
import Footer from '../components/organisms/Footer';
import Typography from '../components/atoms/Typography';
import Card from '../components/atoms/Card';
import { Leaf, Globe, Zap, Recycle } from 'lucide-react';

const Sustainability: React.FC = () => {
  return (
    <div className="min-h-screen surface-void">
      <Header />
      
      <main className="pt-24">
        <div className="container mx-auto px-8 py-16">
          <div className="text-center mb-16">
            <Typography as="h1" variant="4xl" weight="bold" gradient="consciousness" className="mb-6">
              Environmental Impact & Sustainability
            </Typography>
            <Typography as="p" variant="xl" color="secondary" className="max-w-4xl mx-auto">
              Building a symbiotic future means creating technology that enhances rather than depletes our planet's resources.
            </Typography>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            <Card variant="consciousness" className="p-8 text-center rounded-[24px] border-consciousness/25">
              <Leaf className="w-16 h-16 mx-auto mb-6 text-consciousness" />
              <Typography variant="lg" weight="semibold" color="consciousness" className="mb-4">
                Carbon Negative
              </Typography>
              <Typography variant="sm" color="secondary">
                Our ACI systems actively reduce environmental impact
              </Typography>
            </Card>

            <Card variant="creativity" className="p-8 text-center rounded-[24px] border-creativity/25">
              <Globe className="w-16 h-16 mx-auto mb-6 text-creativity" />
              <Typography variant="lg" weight="semibold" color="creativity" className="mb-4">
                Global Scale
              </Typography>
              <Typography variant="sm" color="secondary">
                Worldwide optimization for maximum efficiency
              </Typography>
            </Card>

            <Card variant="intuition" className="p-8 text-center rounded-[24px] border-harmony/25">
              <Zap className="w-16 h-16 mx-auto mb-6 text-harmony" />
              <Typography variant="lg" weight="semibold" color="harmony" className="mb-4">
                Energy Efficient
              </Typography>
              <Typography variant="sm" color="secondary">
                Quantum-inspired algorithms minimize power consumption
              </Typography>
            </Card>

            <Card variant="neural" className="p-8 text-center rounded-[24px] border-consciousness/25">
              <Recycle className="w-16 h-16 mx-auto mb-6 text-consciousness" />
              <Typography variant="lg" weight="semibold" color="consciousness" className="mb-4">
                Circular Economy
              </Typography>
              <Typography variant="sm" color="secondary">
                Waste becomes input in our closed-loop systems
              </Typography>
            </Card>
          </div>

          <div className="grid md:grid-cols-2 gap-12 mb-16">
            <div className="glass-quantum rounded-[32px] p-10 border border-consciousness/30">
              <Typography as="h3" variant="xl" weight="semibold" color="consciousness" className="mb-6">
                Environmental Benefits
              </Typography>
              <ul className="space-y-4">
                <li className="flex items-start space-x-3">
                  <div className="w-2 h-2 rounded-full bg-consciousness mt-2 flex-shrink-0" />
                  <Typography variant="base" color="secondary">
                    85% reduction in industrial waste through intelligent resource optimization
                  </Typography>
                </li>
                <li className="flex items-start space-x-3">
                  <div className="w-2 h-2 rounded-full bg-consciousness mt-2 flex-shrink-0" />
                  <Typography variant="base" color="secondary">
                    Carbon footprint reduction of 60% through smart logistics and automation
                  </Typography>
                </li>
                <li className="flex items-start space-x-3">
                  <div className="w-2 h-2 rounded-full bg-consciousness mt-2 flex-shrink-0" />
                  <Typography variant="base" color="secondary">
                    Renewable energy integration reaches 95% efficiency with ACI coordination
                  </Typography>
                </li>
              </ul>
            </div>

            <div className="glass-quantum rounded-[32px] p-10 border border-intuition/30">
              <Typography as="h3" variant="xl" weight="semibold" color="harmony" className="mb-6">
                Sustainable Innovation
              </Typography>
              <Typography variant="base" color="secondary" className="leading-relaxed">
                Our ACI technology is designed with sustainability at its core. By mimicking natural systems, 
                we create solutions that work in harmony with the environment rather than against it. 
                Each division contributes to a more sustainable future through intelligent resource management 
                and waste reduction.
              </Typography>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Sustainability;
