
/* Morphic Field Component Styles */
@layer components {
  .morphic-field {
    background: linear-gradient(135deg, 
      rgba(224, 224, 224, 0.08) 0%, 
      rgba(224, 224, 224, 0.03) 100%);
    backdrop-filter: blur(24px) saturate(150%) brightness(1.1);
    border: 1px solid rgba(224, 224, 224, 0.12);
    border-radius: var(--morphic-radius-organic);
    transition: all var(--quantum-medium) var(--ease-morphic);
    position: relative;
    overflow: hidden;
  }

  .morphic-field::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, 
      var(--consciousness-glow) 0%, 
      transparent 40%, 
      transparent 60%, 
      var(--consciousness-glow) 100%);
    opacity: 0;
    transition: opacity var(--quantum-medium) var(--ease-sentient);
    pointer-events: none;
  }

  .morphic-field:hover {
    transform: translateY(-4px) scale(1.02);
    border-radius: var(--morphic-radius-cellular);
    border-color: var(--consciousness-primary);
    box-shadow: 
      0 12px 40px rgba(0, 0, 0, 0.3),
      0 0 30px var(--consciousness-glow),
      inset 0 1px 0 rgba(224, 224, 224, 0.15);
  }

  .morphic-field:hover::before {
    opacity: 0.1;
  }

  /* Division-Specific Morphic Fields */
  .morphic-field-symbioautomate {
    border-color: var(--symbioautomate-primary);
    background: linear-gradient(135deg, 
      rgba(0, 255, 127, 0.08) 0%, 
      rgba(224, 224, 224, 0.03) 100%);
  }

  .morphic-field-symbioautomate:hover {
    box-shadow: 
      0 12px 40px rgba(0, 0, 0, 0.3),
      0 0 30px var(--symbioautomate-glow);
  }

  .morphic-field-symbiolabs {
    border-color: var(--symbiolabs-primary);
    background: linear-gradient(135deg, 
      rgba(255, 215, 0, 0.08) 0%, 
      rgba(224, 224, 224, 0.03) 100%);
  }

  .morphic-field-symbiolabs:hover {
    box-shadow: 
      0 12px 40px rgba(0, 0, 0, 0.3),
      0 0 30px var(--symbiolabs-glow);
  }

  /* Sentient Button Base */
  .btn-sentient {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 14px 28px;
    background: linear-gradient(135deg, 
      var(--consciousness-primary) 0%, 
      var(--consciousness-secondary) 100%);
    border: 1px solid var(--consciousness-primary);
    border-radius: var(--morphic-radius-neural);
    color: var(--abyssal-void);
    font-family: 'Inter Variable', sans-serif;
    font-variation-settings: "wght" 600, "opsz" 16;
    font-size: 16px;
    letter-spacing: 0.025em;
    transition: all var(--quantum-medium) var(--ease-morphic);
    cursor: pointer;
    overflow: hidden;
    text-decoration: none;
    user-select: none;
  }

  .btn-sentient::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
      transparent, 
      rgba(255, 255, 255, 0.4), 
      transparent);
    transition: left var(--quantum-slow) var(--ease-organic);
  }

  .btn-sentient:hover {
    transform: translateY(-3px) scale(1.05);
    border-radius: var(--morphic-radius-quantum);
    box-shadow: 
      0 15px 40px rgba(0, 0, 0, 0.4),
      0 0 40px var(--consciousness-emissive),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
    filter: brightness(1.1) saturate(1.2);
  }

  .btn-sentient:hover::before {
    left: 100%;
  }

  .btn-sentient:active {
    transform: translateY(-1px) scale(1.02);
    transition: all var(--quantum-instant) var(--ease-sentient);
  }

  /* Bio-Quantum Glass Effect */
  .glass-quantum {
    background: linear-gradient(135deg, 
      rgba(0, 255, 170, 0.08) 0%, 
      rgba(170, 0, 255, 0.05) 50%,
      rgba(0, 229, 255, 0.03) 100%);
    backdrop-filter: blur(20px) saturate(180%) brightness(1.1);
    border: 1px solid rgba(0, 255, 170, 0.15);
    position: relative;
    overflow: hidden;
  }

  .glass-quantum::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, 
      transparent 30%, 
      rgba(255, 255, 255, 0.1) 50%, 
      transparent 70%);
    transform: translateX(-100%);
    transition: transform 800ms ease-out;
    pointer-events: none;
  }

  .glass-quantum:hover::before {
    transform: translateX(100%);
  }

  /* DNA-Inspired Rounded Borders */
  .rounded-biomorphic { border-radius: 63% 37% 54% 46% / 55% 48% 52% 45%; }
  .rounded-organic { border-radius: 40% 60% 70% 30% / 40% 40% 60% 50%; }
  .rounded-cellular { border-radius: 63% 37% 54% 46% / 55% 48% 52% 45%; }
  .rounded-neural { border-radius: 48% 52% 68% 32% / 42% 61% 39% 58%; }
  .rounded-quantum { border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%; }
  .rounded-flow { border-radius: 50% 20% 80% 40% / 30% 70% 60% 40%; }

  /* Precision Typography Classes */
  .text-precision-display {
    font-family: 'Inter Variable', sans-serif;
    font-variation-settings: "wght" 700, "opsz" 32;
    letter-spacing: -0.02em;
    line-height: 1.1;
  }

  .text-precision-body {
    font-family: 'Inter Variable', sans-serif;
    font-variation-settings: "wght" 400, "opsz" 16;
    letter-spacing: 0.01em;
    line-height: 1.6;
  }

  .text-precision-caption {
    font-family: 'Inter Variable', sans-serif;
    font-variation-settings: "wght" 500, "opsz" 14;
    letter-spacing: 0.025em;
    line-height: 1.4;
  }

  .text-precision-mono {
    font-family: 'JetBrains Mono Variable', monospace;
    font-variation-settings: "wght" 400;
    letter-spacing: 0.02em;
  }

  /* Anticipatory Interaction States */
  .anticipatory-element {
    transition: all var(--quantum-fast) var(--ease-sentient);
  }

  .anticipatory-element:hover {
    animation: anticipatory-hover var(--quantum-medium) var(--ease-morphic) forwards;
  }

  /* Holographic Depth Layers */
  .holographic-layer-1 {
    transform: translateZ(10px);
    filter: brightness(1.1);
  }

  .holographic-layer-2 {
    transform: translateZ(20px);
    filter: brightness(1.2) saturate(1.1);
  }

  .holographic-layer-3 {
    transform: translateZ(30px);
    filter: brightness(1.3) saturate(1.2);
  }
}
