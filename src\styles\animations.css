
/* Quantum Animation Keyframes */
@keyframes quantum-nebulae {
  0% { 
    transform: translateX(0) translateY(0) scale(1) rotate(0deg); 
    opacity: 0.4; 
  }
  33% { 
    transform: translateX(15px) translateY(-8px) scale(1.02) rotate(1deg); 
    opacity: 0.7; 
  }
  66% { 
    transform: translateX(-10px) translateY(12px) scale(0.98) rotate(-0.5deg); 
    opacity: 0.6; 
  }
  100% { 
    transform: translateX(20px) translateY(5px) scale(1.01) rotate(0.8deg); 
    opacity: 0.5; 
  }
}

@keyframes quantum-drift {
  0% { transform: translate(0, 0); }
  25% { transform: translate(-5px, -5px); }
  50% { transform: translate(5px, -10px); }
  75% { transform: translate(-10px, 5px); }
  100% { transform: translate(0, 0); }
}

@keyframes morphic-pulse {
  0%, 100% { 
    transform: scale(1);
    border-radius: var(--morphic-radius-organic);
    filter: brightness(1);
  }
  50% { 
    transform: scale(1.02);
    border-radius: var(--morphic-radius-cellular);
    filter: brightness(1.1);
  }
}

@keyframes sentient-glow {
  0%, 100% { 
    box-shadow: 0 0 20px var(--consciousness-glow);
  }
  50% { 
    box-shadow: 0 0 30px var(--consciousness-emissive), 0 0 60px var(--consciousness-glow);
  }
}

@keyframes anticipatory-hover {
  0% { 
    transform: translateY(0) scale(1);
    filter: brightness(1);
  }
  100% { 
    transform: translateY(-2px) scale(1.02);
    filter: brightness(1.1);
  }
}

/* Bio-Quantum Animation Keyframes */
@keyframes dna-helix-rotation {
  0% { transform: rotateY(0deg) rotateZ(0deg); }
  100% { transform: rotateY(360deg) rotateZ(180deg); }
}

@keyframes quantum-flicker {
  0%, 100% { opacity: 0.8; filter: brightness(1); }
  25% { opacity: 1; filter: brightness(1.3); }
  50% { opacity: 0.6; filter: brightness(0.8); }
  75% { opacity: 0.9; filter: brightness(1.1); }
}

@keyframes neural-pulse {
  0%, 100% { 
    box-shadow: 0 0 15px var(--consciousness-glow);
    transform: scale(1);
  }
  50% { 
    box-shadow: 0 0 25px var(--consciousness-emissive), 0 0 40px var(--consciousness-glow);
    transform: scale(1.05);
  }
}

@keyframes consciousness-wave {
  0% { 
    background-position: 0% 50%;
    filter: hue-rotate(0deg);
  }
  50% { 
    background-position: 100% 50%;
    filter: hue-rotate(90deg);
  }
  100% { 
    background-position: 0% 50%;
    filter: hue-rotate(0deg);
  }
}

@keyframes bio-quantum-flow {
  0% { 
    background-position: 0% 0%;
    transform: translateX(0) scaleX(1);
  }
  33% { 
    background-position: 50% 25%;
    transform: translateX(10px) scaleX(1.1);
  }
  66% { 
    background-position: 100% 75%;
    transform: translateX(-5px) scaleX(0.95);
  }
  100% { 
    background-position: 0% 0%;
    transform: translateX(0) scaleX(1);
  }
}
