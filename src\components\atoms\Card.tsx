
import React from 'react';
import { cn } from '@/lib/utils';

interface CardProps {
  variant?: 'neural' | 'quantum' | 'cellular' | 'consciousness' | 'creativity' | 'intuition';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  customBg?: string;
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  interactive?: boolean;
}

const Card: React.FC<CardProps> = ({
  variant = 'neural',
  padding = 'md',
  customBg,
  children,
  className,
  onClick,
  interactive = false,
  ...props
}) => {
  const getVariantStyles = () => {
    switch (variant) {
      case 'quantum':
        return 'card-quantum glass-quantum border-consciousness/20 hover:border-consciousness/40';
      case 'cellular':
        return 'card-cellular bg-gradient-to-br from-harmony/5 to-white/5 border-harmony/20 hover:border-harmony/40';
      case 'consciousness':
        return 'glass-strong border-consciousness/30 hover:shadow-quantum-primary hover:border-consciousness/50 rounded-xl';
      case 'creativity':
        return 'glass-strong border-creativity/30 hover:shadow-[0_0_20px_rgba(0,153,255,0.3)] hover:border-creativity/50 rounded-xl';
      case 'intuition':
        return 'glass-strong border-intuition/30 hover:shadow-[0_0_20px_rgba(170,0,255,0.3)] hover:border-intuition/50 rounded-xl';
      default:
        return 'card-neural';
    }
  };

  const getPaddingStyles = () => {
    switch (padding) {
      case 'none':
        return 'p-0';
      case 'sm':
        return 'p-3';
      case 'lg':
        return 'p-8';
      default:
        return 'p-5';
    }
  };

  const backgroundStyle = customBg ? { background: customBg } : {};

  const baseStyles = 'relative backdrop-blur-neural transition-all duration-bio ease-quantum';
  const interactiveStyles = interactive || onClick ? 'cursor-pointer hover:scale-[1.02] hover:-translate-y-1' : '';

  return (
    <div
      className={cn(
        baseStyles,
        getVariantStyles(),
        getPaddingStyles(),
        interactiveStyles,
        className
      )}
      style={backgroundStyle}
      onClick={onClick}
      {...props}
    >
      {children}
    </div>
  );
};

export default Card;
