
import React from 'react';
import Header from '../components/organisms/Header';
import Footer from '../components/organisms/Footer';
import Typography from '../components/atoms/Typography';
import Card from '../components/atoms/Card';
import Button from '../components/atoms/Button';
import BioQuantumFlow from '../components/interactive/BioQuantumFlow';
import { Network, Zap, Globe, Settings, ArrowRight } from 'lucide-react';

const Integration: React.FC = () => {
  return (
    <div className="min-h-screen surface-void">
      <Header />
      
      <main className="pt-24">
        <div className="container mx-auto px-8 py-16">
          <div className="text-center mb-16">
            <Typography as="h1" variant="4xl" weight="bold" gradient="consciousness" className="mb-6">
              Integration Layer
            </Typography>
            <Typography as="p" variant="xl" color="secondary" className="max-w-4xl mx-auto">
              Seamless connectivity between all divisions ensuring unified intelligence and data flow.
            </Typography>
          </div>

          <div className="grid md:grid-cols-2 gap-8 mb-16">
            <Card variant="consciousness" className="p-10 rounded-[32px] border-consciousness/25">
              <Network className="w-20 h-20 mb-8 text-consciousness" />
              <Typography variant="xl" weight="semibold" color="consciousness" className="mb-6">
                API Gateway
              </Typography>
              <Typography variant="base" color="secondary" className="leading-relaxed">
                Centralized communication hub that manages all inter-division data exchange 
                with intelligent routing and load balancing.
              </Typography>
            </Card>

            <Card variant="creativity" className="p-10 rounded-[32px] border-creativity/25">
              <Zap className="w-20 h-20 mb-8 text-creativity" />
              <Typography variant="xl" weight="semibold" color="creativity" className="mb-6">
                Real-time Sync
              </Typography>
              <Typography variant="base" color="secondary" className="leading-relaxed">
                Instantaneous data synchronization across all ecosystem components 
                ensuring consistent state and optimal performance.
              </Typography>
            </Card>

            <Card variant="intuition" className="p-10 rounded-[32px] border-harmony/25">
              <Globe className="w-20 h-20 mb-8 text-harmony" />
              <Typography variant="xl" weight="semibold" color="harmony" className="mb-6">
                Global Network
              </Typography>
              <Typography variant="base" color="secondary" className="leading-relaxed">
                Worldwide infrastructure enabling seamless integration regardless 
                of geographic location or system architecture.
              </Typography>
            </Card>

            <Card variant="neural" className="p-10 rounded-[32px] border-consciousness/25">
              <Settings className="w-20 h-20 mb-8 text-consciousness" />
              <Typography variant="xl" weight="semibold" color="consciousness" className="mb-6">
                Auto-Configuration
              </Typography>
              <Typography variant="base" color="secondary" className="leading-relaxed">
                Intelligent system configuration that adapts to new components 
                and optimizes integration pathways automatically.
              </Typography>
            </Card>
          </div>

          {/* Integration Flow Visualization */}
          <div className="mb-16">
            <Card variant="quantum" className="p-8 rounded-[32px] border-consciousness/25">
              <div className="text-center mb-8">
                <Typography variant="2xl" weight="semibold" color="consciousness" className="mb-4">
                  Live Integration Flow
                </Typography>
                <Typography variant="base" color="secondary" className="mb-6">
                  Watch real-time data flow between ecosystem divisions
                </Typography>
                <Button
                  variant="quantum"
                  size="md"
                  leftIcon={<Network className="w-5 h-5" />}
                >
                  Monitor Integration Health
                </Button>
              </div>

              <div className="h-96 rounded-[20px] overflow-hidden">
                <BioQuantumFlow
                  isActive={true}
                  variant="molecular"
                  intensity="medium"
                />
              </div>
            </Card>
          </div>

          <div className="glass-quantum rounded-[32px] p-12 border border-consciousness/30">
            <Typography as="h2" variant="2xl" weight="semibold" color="consciousness" className="mb-8 text-center">
              Unified Intelligence Network
            </Typography>
            <Typography variant="lg" color="secondary" className="leading-relaxed text-center max-w-4xl mx-auto mb-8">
              The integration layer serves as the nervous system of the SymbioWave ecosystem, enabling
              seamless communication and data flow between all divisions while maintaining security,
              performance, and scalability across the entire network.
            </Typography>

            <div className="flex justify-center">
              <Button
                variant="quantum"
                size="lg"
                rightIcon={<ArrowRight className="w-5 h-5" />}
                onClick={() => window.location.href = '/ecosystem'}
              >
                Explore Full Ecosystem
              </Button>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Integration;
