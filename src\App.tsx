
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import TheImperative from "./pages/TheImperative";
import EcosystemFlywheel from "./pages/EcosystemFlywheel";
import Vision from "./pages/Vision";
import AboutUs from "./pages/AboutUs";
import SymbioAutomate from "./pages/SymbioAutomate";
import SymbioCore from "./pages/SymbioCore";
import SymbioLabs from "./pages/SymbioLabs";
import SymbioXchange from "./pages/SymbioXchange";
import SymbioEdge from "./pages/SymbioEdge";
import SymbioImpact from "./pages/SymbioImpact";
import SymbioVentures from "./pages/SymbioVentures";
import SymbioAlliance from "./pages/SymbioAlliance";
import ACITechnology from "./pages/ACITechnology";
import DNAArchitecture from "./pages/DNAArchitecture";
import QuantumProcessing from "./pages/QuantumProcessing";
import BiomimeticNetworks from "./pages/BiomimeticNetworks";
import Ecosystem from "./pages/Ecosystem";
import Integration from "./pages/Integration";
import Sustainability from "./pages/Sustainability";
import AIReadinessAudit from "./pages/AIReadinessAudit";
import Interface from "./pages/Interface";
import Documentation from "./pages/Documentation";
import CaseStudies from "./pages/CaseStudies";
import Careers from "./pages/Careers";
import RequestDemo from "./pages/RequestDemo";
import UseCases from "./pages/UseCases";
import WorkflowAutomation from "./pages/WorkflowAutomation";
import AITransformation from "./pages/AITransformation";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Index />} />
          
          {/* New content-focused pages */}
          <Route path="/the-imperative" element={<TheImperative />} />
          <Route path="/ecosystem-flywheel" element={<EcosystemFlywheel />} />
          <Route path="/vision" element={<Vision />} />
          <Route path="/about-us" element={<AboutUs />} />
          
          {/* Division pages */}
          <Route path="/symbioautomate" element={<SymbioAutomate />} />
          <Route path="/symbiocore" element={<SymbioCore />} />
          <Route path="/symbiolabs" element={<SymbioLabs />} />
          <Route path="/symbioxchange" element={<SymbioXchange />} />
          <Route path="/symbioedge" element={<SymbioEdge />} />
          <Route path="/symbioimpact" element={<SymbioImpact />} />
          <Route path="/symbioventures" element={<SymbioVentures />} />
          <Route path="/symbioalliance" element={<SymbioAlliance />} />
          
          {/* Technology pages */}
          <Route path="/aci-technology" element={<ACITechnology />} />
          <Route path="/dna-architecture" element={<DNAArchitecture />} />
          <Route path="/quantum-processing" element={<QuantumProcessing />} />
          <Route path="/biomimetic-networks" element={<BiomimeticNetworks />} />
          
          {/* Platform pages */}
          <Route path="/ecosystem" element={<Ecosystem />} />
          <Route path="/integration" element={<Integration />} />
          <Route path="/sustainability" element={<Sustainability />} />
          
          {/* SymbioAutomate AI Readiness Audit */}
          <Route path="/ai-readiness-audit" element={<AIReadinessAudit />} />
          
          {/* SymbioAutomate Demo Request */}
          <Route path="/request-demo" element={<RequestDemo />} />
          
          {/* SymbioAutomate Use Cases */}
          <Route path="/use-cases" element={<UseCases />} />
          
          {/* SymbioAutomate Workflow Automation */}
          <Route path="/workflow-automation" element={<WorkflowAutomation />} />
          
          {/* AI Transformation Journey */}
          <Route path="/ai-transformation" element={<AITransformation />} />
          
          {/* Interface page */}
          <Route path="/interface" element={<Interface />} />
          
          {/* Additional pages */}
          <Route path="/documentation" element={<Documentation />} />
          <Route path="/case-studies" element={<CaseStudies />} />
          <Route path="/careers" element={<Careers />} />
          
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
