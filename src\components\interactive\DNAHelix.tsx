
import React, { useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';

interface DNAHelixProps {
  isActive: boolean;
  intensity?: 'subtle' | 'medium' | 'intense';
  color?: 'consciousness' | 'harmony' | 'creativity';
  className?: string;
}

const DNAHelix: React.FC<DNAHelixProps> = ({
  isActive,
  intensity = 'medium',
  color = 'consciousness',
  className
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const resizeCanvas = () => {
      const rect = canvas.getBoundingClientRect();
      canvas.width = rect.width * 2;
      canvas.height = rect.height * 2;
      ctx.scale(2, 2);
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    let time = 0;
    const centerX = canvas.width / 4;
    const centerY = canvas.height / 4;
    const helixHeight = canvas.height / 2;
    const helixRadius = Math.min(centerX, centerY) * 0.3;

    const colorMap = {
      consciousness: 'rgba(0, 229, 255, 0.8)',
      harmony: 'rgba(0, 255, 127, 0.8)',
      creativity: 'rgba(170, 0, 255, 0.8)'
    };

    const animate = () => {
      if (!isActive) {
        animationRef.current = requestAnimationFrame(animate);
        return;
      }

      ctx.clearRect(0, 0, canvas.width / 2, canvas.height / 2);
      
      time += 0.02;
      
      // Draw DNA double helix
      const points1: Array<{x: number, y: number}> = [];
      const points2: Array<{x: number, y: number}> = [];
      
      for (let i = 0; i <= 100; i++) {
        const progress = i / 100;
        const y = progress * helixHeight;
        const angle1 = progress * Math.PI * 4 + time;
        const angle2 = angle1 + Math.PI;
        
        const x1 = centerX + Math.cos(angle1) * helixRadius;
        const x2 = centerX + Math.cos(angle2) * helixRadius;
        
        points1.push({ x: x1, y });
        points2.push({ x: x2, y });
      }

      // Draw helix strands
      ctx.strokeStyle = colorMap[color];
      ctx.lineWidth = 3;
      ctx.shadowBlur = 8;
      ctx.shadowColor = colorMap[color];

      // First strand
      ctx.beginPath();
      points1.forEach((point, index) => {
        if (index === 0) {
          ctx.moveTo(point.x, point.y);
        } else {
          ctx.lineTo(point.x, point.y);
        }
      });
      ctx.stroke();

      // Second strand
      ctx.beginPath();
      points2.forEach((point, index) => {
        if (index === 0) {
          ctx.moveTo(point.x, point.y);
        } else {
          ctx.lineTo(point.x, point.y);
        }
      });
      ctx.stroke();

      // Draw connecting base pairs
      ctx.strokeStyle = colorMap[color].replace('0.8', '0.4');
      ctx.lineWidth = 2;
      ctx.shadowBlur = 4;

      for (let i = 0; i < points1.length; i += 8) {
        const point1 = points1[i];
        const point2 = points2[i];
        
        ctx.beginPath();
        ctx.moveTo(point1.x, point1.y);
        ctx.lineTo(point2.x, point2.y);
        ctx.stroke();

        // Base pair nodes
        ctx.shadowBlur = 6;
        ctx.fillStyle = colorMap[color];
        ctx.beginPath();
        ctx.arc(point1.x, point1.y, 3, 0, Math.PI * 2);
        ctx.fill();
        
        ctx.beginPath();
        ctx.arc(point2.x, point2.y, 3, 0, Math.PI * 2);
        ctx.fill();
      }

      // Quantum particles flowing along the helix
      if (intensity !== 'subtle') {
        const particleCount = intensity === 'intense' ? 20 : 10;
        
        for (let p = 0; p < particleCount; p++) {
          const particleTime = (time * 2 + p * 0.5) % 2;
          const progress = particleTime / 2;
          const index = Math.floor(progress * (points1.length - 1));
          
          if (index < points1.length) {
            const point = p % 2 === 0 ? points1[index] : points2[index];
            
            ctx.shadowBlur = 12;
            ctx.shadowColor = colorMap[color];
            ctx.fillStyle = colorMap[color];
            ctx.beginPath();
            ctx.arc(point.x, point.y, 4, 0, Math.PI * 2);
            ctx.fill();
            
            // Particle trail
            const alpha = 1 - progress;
            ctx.fillStyle = colorMap[color].replace('0.8', alpha.toString());
            ctx.beginPath();
            ctx.arc(point.x, point.y, 8, 0, Math.PI * 2);
            ctx.fill();
          }
        }
      }

      ctx.shadowBlur = 0;
      animationRef.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isActive, intensity, color]);

  return (
    <div className={cn("relative w-full h-full overflow-hidden", className)}>
      <canvas
        ref={canvasRef}
        className="w-full h-full"
        style={{
          background: `radial-gradient(ellipse at center, ${
            color === 'consciousness' ? 'rgba(0, 229, 255, 0.05)' :
            color === 'harmony' ? 'rgba(0, 255, 127, 0.05)' :
            'rgba(170, 0, 255, 0.05)'
          } 0%, transparent 70%)`
        }}
      />
    </div>
  );
};

export default DNAHelix;
