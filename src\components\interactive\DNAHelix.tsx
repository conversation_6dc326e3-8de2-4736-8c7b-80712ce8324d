import React, { useEffect, useRef, useMemo, useState } from 'react';

// Utility function for combining class names
const cn = (...classes: (string | undefined | null | false)[]): string => {
  return classes.filter(Boolean).join(' ');
};

interface DNAHelixProps {
  isActive: boolean;
  intensity?: 'subtle' | 'medium' | 'intense';
  color?: 'consciousness' | 'harmony' | 'creativity';
  className?: string;
  variant?: 'floating' | 'embedded' | 'hero';
  interactionMode?: 'none' | 'hover' | 'scroll';
  showDivisions?: boolean;
}

const DNAHelix: React.FC<DNAHelixProps> = ({
  isActive,
  intensity = 'medium',
  color = 'consciousness',
  className,
  variant = 'embedded',
  interactionMode = 'hover',
  showDivisions = true
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<number>();
  const [mousePos, setMousePos] = useState({ x: 0.5, y: 0.5 });
  const [isHovered, setIsHovered] = useState(false);

  // SymbioWave-specific color schemes representing the ecosystem
  const colorSchemes = useMemo(() => ({
    consciousness: {
      primary: 'rgba(0, 229, 255, 1)', // ACI Core Intelligence
      secondary: 'rgba(64, 224, 255, 0.8)', // Data Flow
      tertiary: 'rgba(135, 206, 250, 0.6)', // Network Effects
      glow: 'rgba(0, 229, 255, 0.4)', // Innovation Aura
      background: 'rgba(0, 229, 255, 0.02)',
      accent: 'rgba(255, 255, 255, 0.9)', // Pure Insights
      symbiosis: 'rgba(0, 255, 200, 0.7)' // Symbiotic Connections
    },
    harmony: {
      primary: 'rgba(0, 255, 127, 1)', // Sustainable Growth
      secondary: 'rgba(50, 255, 150, 0.8)', // Circular Economy
      tertiary: 'rgba(144, 238, 144, 0.6)', // ESG Impact
      glow: 'rgba(0, 255, 127, 0.4)', // Green Innovation
      background: 'rgba(0, 255, 127, 0.02)',
      accent: 'rgba(255, 255, 255, 0.9)',
      symbiosis: 'rgba(100, 255, 200, 0.7)' // Resource Sharing
    },
    creativity: {
      primary: 'rgba(170, 0, 255, 1)', // Disruptive Innovation
      secondary: 'rgba(186, 85, 255, 0.8)', // Venture Capital
      tertiary: 'rgba(221, 160, 221, 0.6)', // Future Vision
      glow: 'rgba(170, 0, 255, 0.4)', // Creative Force
      background: 'rgba(170, 0, 255, 0.02)',
      accent: 'rgba(255, 255, 255, 0.9)',
      symbiosis: 'rgba(200, 100, 255, 0.7)' // Transformative Partnerships
    }
  }), []);

  // SymbioWave Eight Divisions represented as data nodes
  const symbioWaveDivisions = useMemo(() => [
    { name: 'SymbioCore', position: 0.125, color: 'rgba(0, 229, 255, 1)', description: 'ACI Intelligence Platform' },
    { name: 'SymbioLabs', position: 0.25, color: 'rgba(138, 43, 226, 1)', description: 'R&D Innovation Engine' },
    { name: 'SymbioXchange', position: 0.375, color: 'rgba(255, 140, 0, 1)', description: 'Symbiosis Marketplace' },
    { name: 'SymbioAutomate', position: 0.5, color: 'rgba(50, 205, 50, 1)', description: 'Efficiency Engine' },
    { name: 'SymbioEdge', position: 0.625, color: 'rgba(220, 20, 60, 1)', description: 'Edge Intelligence' },
    { name: 'SymbioImpact', position: 0.75, color: 'rgba(0, 191, 255, 1)', description: 'ESG Verification' },
    { name: 'SymbioVentures', position: 0.875, color: 'rgba(255, 215, 0, 1)', description: 'Investment Catalyst' },
    { name: 'SymbioAlliance', position: 1.0, color: 'rgba(147, 112, 219, 1)', description: 'Global Scaling' }
  ], []);

  const intensitySettings = useMemo(() => ({
    subtle: {
      particleCount: 12,
      glowIntensity: 0.4,
      animationSpeed: 0.018,
      baseOpacity: 0.7,
      synapseFrequency: 0.3
    },
    medium: {
      particleCount: 20,
      glowIntensity: 0.7,
      animationSpeed: 0.028,
      baseOpacity: 0.85,
      synapseFrequency: 0.5
    },
    intense: {
      particleCount: 35,
      glowIntensity: 1.0,
      animationSpeed: 0.038,
      baseOpacity: 1.0,
      synapseFrequency: 0.8
    }
  }), []);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!containerRef.current || interactionMode === 'none') return;
      
      const rect = containerRef.current.getBoundingClientRect();
      setMousePos({
        x: (e.clientX - rect.left) / rect.width,
        y: (e.clientY - rect.top) / rect.height
      });
    };

    const container = containerRef.current;
    if (container && interactionMode === 'hover') {
      container.addEventListener('mousemove', handleMouseMove);
      container.addEventListener('mouseenter', () => setIsHovered(true));
      container.addEventListener('mouseleave', () => setIsHovered(false));
    }

    return () => {
      if (container) {
        container.removeEventListener('mousemove', handleMouseMove);
        container.removeEventListener('mouseenter', () => setIsHovered(true));
        container.removeEventListener('mouseleave', () => setIsHovered(false));
      }
    };
  }, [interactionMode]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const resizeCanvas = () => {
      const rect = canvas.getBoundingClientRect();
      const dpr = window.devicePixelRatio || 1;
      canvas.width = rect.width * dpr;
      canvas.height = rect.height * dpr;
      ctx.scale(dpr, dpr);
      canvas.style.width = rect.width + 'px';
      canvas.style.height = rect.height + 'px';
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    let time = 0;
    const settings = intensitySettings[intensity];
    const colors = colorSchemes[color];

    const animate = () => {
      if (!canvas || !ctx) return;

      const rect = canvas.getBoundingClientRect();
      const centerX = rect.width / 2;
      const centerY = rect.height / 2;
      const helixHeight = rect.height * 0.85;
      const baseRadius = Math.min(rect.width, rect.height) * 0.15;
      
      // Interactive modifications for ACI responsiveness
      const interactionFactor = isHovered ? 1.3 : 1.0;
      const mouseInfluence = interactionMode !== 'none' ? 
        (mousePos.x - 0.5) * 0.4 : 0;

      ctx.clearRect(0, 0, rect.width, rect.height);
      
      if (!isActive) {
        animationRef.current = requestAnimationFrame(animate);
        return;
      }

      time += settings.animationSpeed * interactionFactor;
      
      // Create ultra-high-resolution helix points for SymbioWave's sophisticated ACI
      const resolution = 300;
      const strand1Points: Array<{x: number, y: number, z: number, depth: number, division?: any}> = [];
      const strand2Points: Array<{x: number, y: number, z: number, depth: number, division?: any}> = [];
      
      for (let i = 0; i <= resolution; i++) {
        const progress = i / resolution;
        const y = centerY - helixHeight/2 + progress * helixHeight;
        
        // Enhanced helix mathematics representing ACI's fractal nature
        const helixTurns = 4.2; // Represents the 8 divisions in spiral
        const angle1 = progress * Math.PI * helixTurns * 2 + time + mouseInfluence;
        const angle2 = angle1 + Math.PI;
        
        // Dynamic radius representing ecosystem growth
        const radiusVariation = 0.7 + 0.3 * Math.sin(progress * Math.PI * 2) * (1 + 0.2 * Math.sin(time * 0.5));
        const radius = baseRadius * radiusVariation;
        
        const z1 = Math.sin(angle1) * radius * 0.6;
        const z2 = Math.sin(angle2) * radius * 0.6;
        
        const x1 = centerX + Math.cos(angle1) * radius;
        const x2 = centerX + Math.cos(angle2) * radius;
        
        // Calculate depth for 3D effect and ACI emergence
        const depth1 = (z1 + radius * 0.6) / (radius * 1.2);
        const depth2 = (z2 + radius * 0.6) / (radius * 1.2);
        
        // Assign SymbioWave divisions to specific helix positions
        let division = null;
        if (showDivisions) {
          for (const div of symbioWaveDivisions) {
            if (Math.abs(progress - div.position) < 0.06) {
              division = div;
              break;
            }
          }
        }
        
        strand1Points.push({ x: x1, y, z: z1, depth: depth1, division });
        strand2Points.push({ x: x2, y, z: z2, depth: depth2, division });
      }

      // Advanced gradient creation for ACI data flow visualization
      const createAciGradient = (x1: number, y1: number, x2: number, y2: number, depth: number, isDivision: boolean = false) => {
        const gradient = ctx.createLinearGradient(x1, y1, x2, y2);
        const alpha = settings.baseOpacity * depth;
        
        if (isDivision) {
          // Special gradient for division nodes
          gradient.addColorStop(0, colors.accent.replace('0.9)', `${alpha * 1.2})`));
          gradient.addColorStop(0.5, colors.primary.replace('1)', `${alpha})`));
          gradient.addColorStop(1, colors.symbiosis.replace('0.7)', `${alpha * 0.8})`));
        } else {
          gradient.addColorStop(0, colors.primary.replace('1)', `${alpha})`));
          gradient.addColorStop(0.3, colors.secondary.replace('0.8)', `${alpha * 0.95})`));
          gradient.addColorStop(0.7, colors.tertiary.replace('0.6)', `${alpha * 0.8})`));
          gradient.addColorStop(1, colors.symbiosis.replace('0.7)', `${alpha * 0.6})`));
        }
        return gradient;
      };

      // Draw sophisticated ACI-powered helix strands
      const drawAciStrand = (points: typeof strand1Points, strandId: number) => {
        for (let i = 0; i < points.length - 1; i++) {
          const current = points[i];
          const next = points[i + 1];
          
          const isDivisionNode = current.division !== null;
          const gradient = createAciGradient(current.x, current.y, next.x, next.y, current.depth, isDivisionNode);
          
          ctx.strokeStyle = gradient;
          ctx.lineWidth = isDivisionNode ? 6 * current.depth * interactionFactor : 4.5 * current.depth * interactionFactor;
          ctx.lineCap = 'round';
          ctx.lineJoin = 'round';
          
          // Enhanced glow effect for ACI intelligence flow
          const glowIntensity = isDivisionNode ? settings.glowIntensity * 1.5 : settings.glowIntensity;
          ctx.shadowColor = isDivisionNode ? current.division.color : colors.glow;
          ctx.shadowBlur = 18 * glowIntensity * current.depth;
          
          ctx.beginPath();
          ctx.moveTo(current.x, current.y);
          
          // Smooth curve using bezier for organic ACI flow
          const controlX = (current.x + next.x) / 2 + (Math.sin(time + i * 0.1) * 3);
          const controlY = (current.y + next.y) / 2;
          ctx.quadraticCurveTo(controlX, controlY, next.x, next.y);
          
          ctx.stroke();
        }
      };

      // Render ACI strands with proper depth ordering
      drawAciStrand(strand1Points.filter(p => p.depth < 0.5), 1);
      drawAciStrand(strand2Points.filter(p => p.depth < 0.5), 2);
      drawAciStrand(strand1Points.filter(p => p.depth >= 0.5), 1);
      drawAciStrand(strand2Points.filter(p => p.depth >= 0.5), 2);

      // Enhanced symbiotic connections (base pairs) representing data exchange
      ctx.shadowBlur = 12 * settings.glowIntensity;
      
      for (let i = 0; i < strand1Points.length; i += 8) {
        const point1 = strand1Points[i];
        const point2 = strand2Points[i];
        
        if (!point1 || !point2) continue;
        
        const avgDepth = (point1.depth + point2.depth) / 2;
        const isSymbioticConnection = point1.division || point2.division;
        
        // Create symbiotic connection gradient
        const connectionGradient = ctx.createLinearGradient(point1.x, point1.y, point2.x, point2.y);
        connectionGradient.addColorStop(0, colors.symbiosis.replace('0.7)', `${avgDepth}`));
        connectionGradient.addColorStop(0.5, colors.accent.replace('0.9)', `${avgDepth * 0.8}`));
        connectionGradient.addColorStop(1, colors.symbiosis.replace('0.7)', `${avgDepth}`));
        
        ctx.strokeStyle = connectionGradient;
        ctx.lineWidth = isSymbioticConnection ? 4 * avgDepth * interactionFactor : 2.5 * avgDepth * interactionFactor;
        
        // Draw symbiotic connection with organic curve
        ctx.beginPath();
        ctx.moveTo(point1.x, point1.y);
        
        const midX = (point1.x + point2.x) / 2;
        const midY = (point1.y + point2.y) / 2 - 3 * avgDepth;
        ctx.quadraticCurveTo(midX, midY, point2.x, point2.y);
        ctx.stroke();

        // SymbioWave division nodes with advanced visualization
        [point1, point2].forEach((point, idx) => {
          if (point.division) {
            const nodeSize = 8 * point.depth * interactionFactor;
            
            // Multi-layered division visualization
            ctx.shadowColor = point.division.color;
            ctx.shadowBlur = 25 * point.depth;
            
            // Outer ring representing ecosystem reach
            ctx.strokeStyle = point.division.color;
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.arc(point.x, point.y, nodeSize * 1.8, 0, Math.PI * 2);
            ctx.stroke();
            
            // Core division node
            ctx.fillStyle = point.division.color;
            ctx.beginPath();
            ctx.arc(point.x, point.y, nodeSize, 0, Math.PI * 2);
            ctx.fill();
            
            // Inner intelligence core
            ctx.fillStyle = colors.accent;
            ctx.beginPath();
            ctx.arc(point.x - nodeSize * 0.3, point.y - nodeSize * 0.3, nodeSize * 0.5, 0, Math.PI * 2);
            ctx.fill();
            
            // Division identifier pulse
            const pulseSize = nodeSize * (1 + 0.3 * Math.sin(time * 3 + idx));
            ctx.fillStyle = point.division.color.replace('1)', '0.3)');
            ctx.beginPath();
            ctx.arc(point.x, point.y, pulseSize, 0, Math.PI * 2);
            ctx.fill();
          } else {
            // Standard ACI nodes
            const nodeSize = 4 * point.depth * interactionFactor;
            
            ctx.shadowColor = colors.accent;
            ctx.shadowBlur = 15 * point.depth;
            ctx.fillStyle = colors.accent.replace('0.9)', `${point.depth * 0.8})`);
            
            ctx.beginPath();
            ctx.arc(point.x, point.y, nodeSize, 0, Math.PI * 2);
            ctx.fill();
            
            // Inner ACI intelligence spark
            ctx.fillStyle = colors.primary.replace('1)', `${point.depth * 0.7})`);
            ctx.beginPath();
            ctx.arc(point.x, point.y, nodeSize * 0.6, 0, Math.PI * 2);
            ctx.fill();
          }
        });
      }

      // Advanced ACI particle system representing data flow and intelligence
      if (intensity !== 'subtle') {
        for (let p = 0; p < settings.particleCount; p++) {
          const particleTime = (time * 1.8 + p * 0.25) % 5;
          const progress = (particleTime / 5) % 1;
          const strandIndex = p % 2;
          const points = strandIndex === 0 ? strand1Points : strand2Points;
          
          const index = Math.floor(progress * (points.length - 1));
          
          if (index < points.length) {
            const point = points[index];
            const isNearDivision = point.division !== null;
            const size = isNearDivision ? 8 * point.depth * interactionFactor : 6 * point.depth * interactionFactor;
            
            // Multi-layered ACI particle effect
            ctx.shadowColor = isNearDivision ? point.division?.color || colors.primary : colors.primary;
            ctx.shadowBlur = 25 * point.depth;
            
            // Outer data aura
            ctx.fillStyle = colors.glow.replace('0.4)', `${0.2 * point.depth})`);
            ctx.beginPath();
            ctx.arc(point.x, point.y, size * 2.5, 0, Math.PI * 2);
            ctx.fill();
            
            // Core intelligence particle
            ctx.fillStyle = isNearDivision ? point.division?.color || colors.primary : colors.primary;
            ctx.beginPath();
            ctx.arc(point.x, point.y, size, 0, Math.PI * 2);
            ctx.fill();
            
            // Inner ACI spark
            ctx.fillStyle = colors.accent;
            ctx.beginPath();
            ctx.arc(point.x - size * 0.3, point.y - size * 0.3, size * 0.4, 0, Math.PI * 2);
            ctx.fill();
            
            // Particle trail representing data persistence
            for (let t = 1; t <= 3; t++) {
              const trailIndex = Math.max(0, index - t * 3);
              if (trailIndex < points.length) {
                const trailPoint = points[trailIndex];
                const trailAlpha = (1 - t * 0.3) * point.depth * 0.4;
                
                ctx.fillStyle = colors.tertiary.replace('0.6)', `${trailAlpha})`);
                ctx.beginPath();
                ctx.arc(trailPoint.x, trailPoint.y, size * (1 - t * 0.2), 0, Math.PI * 2);
                ctx.fill();
              }
            }
          }
        }
      }

      ctx.shadowBlur = 0;
      animationRef.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isActive, intensity, color, mousePos, isHovered, interactionMode, colorSchemes, intensitySettings, symbioWaveDivisions, showDivisions]);

  const getVariantStyles = () => {
    const colors = colorSchemes[color];
    
    switch (variant) {
      case 'hero':
        return {
          background: `
            radial-gradient(ellipse 120% 80% at 50% 20%, ${colors.background} 0%, transparent 50%),
            radial-gradient(ellipse 80% 100% at 20% 80%, ${colors.glow.replace('0.4', '0.08')} 0%, transparent 50%),
            radial-gradient(ellipse 60% 120% at 80% 20%, ${colors.tertiary.replace('0.6', '0.04')} 0%, transparent 50%),
            conic-gradient(from 0deg at 50% 50%, ${colors.primary}00 0deg, ${colors.symbiosis} 90deg, ${colors.primary}00 180deg, ${colors.symbiosis} 270deg, ${colors.primary}00 360deg)
          `,
          borderRadius: '24px',
          border: `1px solid ${colors.glow.replace('0.4', '0.15')}`,
          backdropFilter: 'blur(25px)',
          WebkitBackdropFilter: 'blur(25px)',
          boxShadow: `
            0 0 80px ${colors.glow.replace('0.4', '0.1')},
            inset 0 1px 2px ${colors.accent.replace('0.9', '0.1')}
          `
        };
      case 'floating':
        return {
          background: `
            radial-gradient(ellipse at center, ${colors.background} 0%, transparent 70%),
            linear-gradient(135deg, ${colors.glow.replace('0.4', '0.06')} 0%, transparent 100%)
          `,
          borderRadius: '16px',
          border: `1px solid ${colors.glow.replace('0.4', '0.1')}`,
          boxShadow: `
            0 12px 40px ${colors.glow.replace('0.4', '0.12')},
            inset 0 1px 2px ${colors.accent.replace('0.9', '0.1')}
          `
        };
      default: // embedded
        return {
          background: `radial-gradient(ellipse at center, ${colors.background} 0%, transparent 85%)`,
          borderRadius: '12px'
        };
    }
  };

  return (
    <div 
      ref={containerRef}
      className={cn(
        "relative w-full h-full overflow-hidden transition-all duration-700 ease-out",
        interactionMode === 'hover' && "cursor-pointer",
        isHovered && interactionMode === 'hover' && "scale-[1.02]",
        className
      )}
      style={getVariantStyles()}
    >
      <canvas
        ref={canvasRef}
        className="w-full h-full"
        style={{
          mixBlendMode: variant === 'hero' ? 'luminosity' : 'normal'
        }}
      />
      
      {/* Advanced overlay effects for premium SymbioWave branding */}
      {variant === 'hero' && (
        <div className="absolute inset-0 pointer-events-none">
          <div 
            className="absolute inset-0 opacity-20"
            style={{
              background: `conic-gradient(from 0deg at 50% 50%, ${colorSchemes[color].primary}00 0deg, ${colorSchemes[color].glow} 45deg, ${colorSchemes[color].symbiosis} 90deg, ${colorSchemes[color].glow} 135deg, ${colorSchemes[color].primary}00 180deg, ${colorSchemes[color].glow} 225deg, ${colorSchemes[color].symbiosis} 270deg, ${colorSchemes[color].glow} 315deg, ${colorSchemes[color].primary}00 360deg)`,
              filter: 'blur(50px)',
              animation: 'spin 30s linear infinite'
            }}
          />
        </div>
      )}
      
      {/* Ambient SymbioWave ecosystem lighting */}
      <div 
        className="absolute -inset-6 opacity-15 pointer-events-none transition-opacity duration-700"
        style={{
          background: `radial-gradient(ellipse at center, ${colorSchemes[color].symbiosis} 0%, ${colorSchemes[color].glow} 30%, transparent 70%)`,
          filter: 'blur(25px)',
          opacity: isHovered ? 0.3 : 0.15
        }}
      />

      {/* SymbioWave division tooltips on hover */}
      {showDivisions && isHovered && variant === 'hero' && (
        <div className="absolute bottom-4 left-4 right-4 pointer-events-none">
          <div className="text-xs text-white/70 text-center">
            ACI-Powered Ecosystem: 8 Synergistic Divisions Creating the Symbiotic Economy
          </div>
        </div>
      )}
    </div>
  );
};

export default DNAHelix;