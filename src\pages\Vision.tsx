
import React from 'react';
import Header from '../components/organisms/Header';
import Footer from '../components/organisms/Footer';
import Typography from '../components/atoms/Typography';
import Card from '../components/atoms/Card';
import Button from '../components/atoms/Button';
import { Eye, Lightbulb, Globe, Zap, ArrowRight, Users, Target } from 'lucide-react';

const Vision: React.FC = () => {
  const visionPillars = [
    {
      icon: Eye,
      title: "Symbiotic Intelligence",
      description: "A future where AI and human intelligence work in perfect harmony, each amplifying the other's strengths.",
      timeframe: "2025-2030"
    },
    {
      icon: Globe,
      title: "Global Accessibility",
      description: "Advanced AI capabilities available to every organization, regardless of size or technical expertise.",
      timeframe: "2026-2028"
    },
    {
      icon: Lightbulb,
      title: "Emergent Solutions",
      description: "Complex global challenges solved through bio-inspired collective intelligence patterns.",
      timeframe: "2028-2035"
    }
  ];

  const futureScenarios = [
    {
      title: "Healthcare Revolution",
      description: "ACI systems discover personalized treatments by analyzing patterns across millions of cellular interactions.",
      impact: "Personalized medicine for all"
    },
    {
      title: "Climate Optimization",
      description: "Symbiotic networks coordinate global resources in real-time, optimizing for sustainability and efficiency.",
      impact: "Carbon neutrality by 2030"
    },
    {
      title: "Economic Symbiosis",
      description: "Businesses operate as living ecosystems, where value flows naturally to where it's most needed.",
      impact: "Post-scarcity economics"
    }
  ];

  const milestones = [
    { year: "2025", achievement: "SymbioCore reaches 1M+ active ACI models" },
    { year: "2026", achievement: "SymbioAutomate automates 50% of business processes" },
    { year: "2027", achievement: "SymbioXchange enables $100B+ in optimized resource allocation" },
    { year: "2028", achievement: "SymbioImpact demonstrates measurable global sustainability impact" },
    { year: "2030", achievement: "Symbiotic intelligence becomes the dominant paradigm" }
  ];

  return (
    <div className="min-h-screen surface-void">
      <Header />
      
      <main>
        {/* Hero Section */}
        <section className="pt-32 pb-20 px-6">
          <div className="container mx-auto text-center">
            <Typography 
              as="h1" 
              variant="4xl" 
              weight="bold" 
              gradient="consciousness"
              className="mb-6"
            >
              Envisioning the Symbiotic Future
            </Typography>
            <Typography 
              variant="xl" 
              color="secondary"
              className="max-w-3xl mx-auto mb-8"
            >
              A world where artificial and human intelligence converge to create 
              unprecedented solutions to humanity's greatest challenges.
            </Typography>
            <Typography 
              variant="lg" 
              color="consciousness"
              className="max-w-2xl mx-auto"
            >
              We're not just building technology. We're architecting the next phase 
              of human civilization through symbiotic intelligence systems.
            </Typography>
          </div>
        </section>

        {/* Vision Pillars */}
        <section className="py-20 bg-gradient-to-b from-surface-void/50 to-surface-void">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <Typography 
                as="h2" 
                variant="3xl" 
                weight="bold" 
                color="consciousness"
                className="mb-4"
              >
                The Three Pillars of Our Vision
              </Typography>
              <Typography 
                variant="lg" 
                color="secondary"
                className="max-w-2xl mx-auto"
              >
                The foundational principles that will guide the transformation to symbiotic intelligence
              </Typography>
            </div>

            <div className="grid lg:grid-cols-3 gap-8">
              {visionPillars.map((pillar, index) => {
                const Icon = pillar.icon;
                return (
                  <Card key={index} variant="quantum" className="p-8 text-center">
                    <div className="w-20 h-20 rounded-cellular bg-consciousness/20 flex items-center justify-center mx-auto mb-6">
                      <Icon className="w-10 h-10 text-consciousness" />
                    </div>
                    
                    <Typography 
                      as="h3" 
                      variant="xl" 
                      weight="semibold" 
                      color="consciousness"
                      className="mb-4"
                    >
                      {pillar.title}
                    </Typography>
                    
                    <Typography 
                      variant="sm" 
                      color="secondary"
                      className="mb-6 leading-relaxed"
                    >
                      {pillar.description}
                    </Typography>

                    <div className="flex items-center justify-center space-x-2">
                      <div className="w-2 h-2 rounded-full bg-creativity"></div>
                      <Typography 
                        variant="xs" 
                        color="creativity"
                        weight="medium"
                      >
                        Timeline: {pillar.timeframe}
                      </Typography>
                    </div>
                  </Card>
                );
              })}
            </div>
          </div>
        </section>

        {/* Future Scenarios */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <Typography 
                as="h2" 
                variant="3xl" 
                weight="bold" 
                gradient="neural"
                className="mb-4"
              >
                Future Scenarios
              </Typography>
              <Typography 
                variant="lg" 
                color="secondary"
                className="max-w-2xl mx-auto"
              >
                Real-world applications of symbiotic intelligence that will transform society
              </Typography>
            </div>

            <div className="space-y-8">
              {futureScenarios.map((scenario, index) => (
                <Card key={index} variant="neural" className="p-8">
                  <div className="grid lg:grid-cols-3 gap-6 items-center">
                    <div className="lg:col-span-2">
                      <Typography 
                        as="h3" 
                        variant="xl" 
                        weight="semibold" 
                        color="consciousness"
                        className="mb-3"
                      >
                        {scenario.title}
                      </Typography>
                      <Typography 
                        variant="sm" 
                        color="secondary"
                        className="leading-relaxed"
                      >
                        {scenario.description}
                      </Typography>
                    </div>
                    <div className="text-center lg:text-right">
                      <div className="inline-flex items-center space-x-2 px-4 py-2 bg-consciousness/10 border border-consciousness/30 rounded-cellular">
                        <Target className="w-4 h-4 text-consciousness" />
                        <Typography 
                          variant="sm" 
                          color="consciousness"
                          weight="medium"
                        >
                          {scenario.impact}
                        </Typography>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Roadmap */}
        <section className="py-20 bg-gradient-to-b from-surface-void/50 to-surface-void">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <Typography 
                as="h2" 
                variant="3xl" 
                weight="bold" 
                color="consciousness"
                className="mb-4"
              >
                Our Roadmap to the Future
              </Typography>
              <Typography 
                variant="lg" 
                color="secondary"
                className="max-w-2xl mx-auto"
              >
                Key milestones on the journey to symbiotic intelligence
              </Typography>
            </div>

            <div className="space-y-6">
              {milestones.map((milestone, index) => (
                <Card key={index} variant="neural" className="p-6">
                  <div className="flex items-center space-x-6">
                    <div className="w-20 h-20 rounded-cellular bg-gradient-to-br from-consciousness/20 to-creativity/10 flex items-center justify-center flex-shrink-0">
                      <Typography 
                        variant="lg" 
                        weight="bold" 
                        color="consciousness"
                      >
                        {milestone.year}
                      </Typography>
                    </div>
                    <Typography 
                      variant="lg" 
                      color="secondary"
                      className="flex-1"
                    >
                      {milestone.achievement}
                    </Typography>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="py-20">
          <div className="container mx-auto px-6 text-center">
            <Typography 
              as="h2" 
              variant="3xl" 
              weight="bold" 
              gradient="consciousness"
              className="mb-6"
            >
              Shape the Future with Us
            </Typography>
            <Typography 
              variant="lg" 
              color="secondary"
              className="max-w-2xl mx-auto mb-8"
            >
              The symbiotic future isn't inevitable - it requires conscious choice and 
              collective action. Join us in building the intelligent systems that will 
              define the next century.
            </Typography>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                variant="quantum" 
                size="lg"
                rightIcon={<ArrowRight className="w-5 h-5" />}
              >
                Start Your Journey
              </Button>
              <Button 
                variant="outline-quantum" 
                size="lg"
              >
                Partner with Us
              </Button>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default Vision;
