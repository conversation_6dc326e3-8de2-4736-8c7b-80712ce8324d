
import React from 'react';
import Header from '../components/organisms/Header';
import Footer from '../components/organisms/Footer';
import Typography from '../components/atoms/Typography';
import Card from '../components/atoms/Card';
import { Network, Leaf, Brain, Zap } from 'lucide-react';

const BiomimeticNetworks: React.FC = () => {
  return (
    <div className="min-h-screen surface-void">
      <Header />
      
      <main className="pt-24">
        <div className="container mx-auto px-8 py-16">
          <div className="text-center mb-16">
            <Typography as="h1" variant="4xl" weight="bold" gradient="consciousness" className="mb-6">
              Bio-Mimetic Networks
            </Typography>
            <Typography as="p" variant="xl" color="secondary" className="max-w-4xl mx-auto">
              Nature-inspired network designs that replicate the efficiency and resilience of biological systems.
            </Typography>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            <Card variant="consciousness" className="p-8 text-center rounded-[24px] border-consciousness/25">
              <Network className="w-16 h-16 mx-auto mb-6 text-consciousness" />
              <Typography variant="lg" weight="semibold" color="consciousness" className="mb-4">
                Neural Networks
              </Typography>
              <Typography variant="sm" color="secondary">
                Brain-inspired architectures
              </Typography>
            </Card>

            <Card variant="creativity" className="p-8 text-center rounded-[24px] border-creativity/25">
              <Leaf className="w-16 h-16 mx-auto mb-6 text-creativity" />
              <Typography variant="lg" weight="semibold" color="creativity" className="mb-4">
                Organic Patterns
              </Typography>
              <Typography variant="sm" color="secondary">
                Natural growth algorithms
              </Typography>
            </Card>

            <Card variant="intuition" className="p-8 text-center rounded-[24px] border-harmony/25">
              <Brain className="w-16 h-16 mx-auto mb-6 text-harmony" />
              <Typography variant="lg" weight="semibold" color="harmony" className="mb-4">
                Synaptic Learning
              </Typography>
              <Typography variant="sm" color="secondary">
                Adaptive connection strength
              </Typography>
            </Card>

            <Card variant="neural" className="p-8 text-center rounded-[24px] border-consciousness/25">
              <Zap className="w-16 h-16 mx-auto mb-6 text-consciousness" />
              <Typography variant="lg" weight="semibold" color="consciousness" className="mb-4">
                Emergent Behavior
              </Typography>
              <Typography variant="sm" color="secondary">
                Complex system emergence
              </Typography>
            </Card>
          </div>

          <div className="glass-quantum rounded-[32px] p-12 border border-consciousness/30">
            <Typography as="h2" variant="2xl" weight="semibold" color="consciousness" className="mb-8">
              Learning from Nature's Wisdom
            </Typography>
            <Typography variant="lg" color="secondary" className="leading-relaxed">
              Billions of years of evolution have perfected network designs in nature. Our bio-mimetic networks 
              replicate these time-tested patterns, creating artificial systems with the resilience, efficiency, 
              and adaptability found in biological neural networks and organic growth patterns.
            </Typography>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default BiomimeticNetworks;
