import React, { useState, useEffect, useRef } from 'react';

interface DNAStrandAnimationProps {
  isHovering: boolean;
  mousePosition: { x: number; y: number };
}

const DNAStrandAnimation: React.FC<DNAStrandAnimationProps> = ({ isHovering, mousePosition }) => {
  const [particles, setParticles] = useState<Array<{
    id: number;
    x: number;
    y: number;
    vx: number;
    vy: number;
    life: number;
    maxLife: number;
    type: 'nucleotide' | 'bond' | 'helix';
  }>>([]);
  
  const animationRef = useRef<number>();
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!isHovering) {
      setParticles([]);
      return;
    }

    const createParticle = (type: 'nucleotide' | 'bond' | 'helix', x: number, y: number) => ({
      id: Math.random(),
      x,
      y,
      vx: (Math.random() - 0.5) * 2,
      vy: (Math.random() - 0.5) * 2,
      life: 0,
      maxLife: 60 + Math.random() * 40,
      type
    });

    const animate = () => {
      if (!containerRef.current) return;
      
      const rect = containerRef.current.getBoundingClientRect();
      const centerX = rect.width / 2;
      const centerY = rect.height / 2;
      
      setParticles(prev => {
        let newParticles = [...prev];
        
        // Add new particles based on mouse position
        if (isHovering && newParticles.length < 50) {
          const mouseX = mousePosition.x * rect.width;
          const mouseY = mousePosition.y * rect.height;
          
          // Create DNA helix particles
          for (let i = 0; i < 3; i++) {
            const angle = (Date.now() * 0.01 + i * 2) % (Math.PI * 2);
            const radius = 30 + Math.sin(Date.now() * 0.005) * 10;
            
            newParticles.push(createParticle(
              'helix',
              mouseX + Math.cos(angle) * radius,
              mouseY + Math.sin(angle) * radius
            ));
            
            newParticles.push(createParticle(
              'helix',
              mouseX + Math.cos(angle + Math.PI) * radius,
              mouseY + Math.sin(angle + Math.PI) * radius
            ));
          }
          
          // Create nucleotide particles
          if (Math.random() < 0.3) {
            newParticles.push(createParticle('nucleotide', mouseX, mouseY));
          }
          
          // Create bond particles
          if (Math.random() < 0.2) {
            newParticles.push(createParticle('bond', mouseX, mouseY));
          }
        }
        
        // Update existing particles
        return newParticles
          .map(particle => ({
            ...particle,
            x: particle.x + particle.vx,
            y: particle.y + particle.vy,
            life: particle.life + 1,
            vx: particle.vx * 0.98,
            vy: particle.vy * 0.98
          }))
          .filter(particle => particle.life < particle.maxLife);
      });
      
      animationRef.current = requestAnimationFrame(animate);
    };

    animate();
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isHovering, mousePosition]);

  return (
    <div 
      ref={containerRef}
      className="absolute inset-0 pointer-events-none overflow-hidden"
      style={{ zIndex: 5 }}
    >
      {/* DNA Helix Strands */}
      {isHovering && (
        <svg className="absolute inset-0 w-full h-full" style={{ zIndex: 1 }}>
          <defs>
            <linearGradient id="helixGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="rgb(59, 130, 246)" stopOpacity="0.6" />
              <stop offset="50%" stopColor="rgb(147, 51, 234)" stopOpacity="0.8" />
              <stop offset="100%" stopColor="rgb(236, 72, 153)" stopOpacity="0.6" />
            </linearGradient>
            <linearGradient id="helixGradient2" x1="100%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="rgb(236, 72, 153)" stopOpacity="0.6" />
              <stop offset="50%" stopColor="rgb(59, 130, 246)" stopOpacity="0.8" />
              <stop offset="100%" stopColor="rgb(147, 51, 234)" stopOpacity="0.6" />
            </linearGradient>
          </defs>
          
          {/* Dynamic Helix Paths */}
          {[...Array(8)].map((_, i) => {
            const time = Date.now() * 0.002;
            const offset = i * 0.5;
            const amplitude = 40 + Math.sin(time + offset) * 10;
            const frequency = 0.02;
            
            let path1 = `M 0 ${50 + amplitude * Math.sin(offset)}`;
            let path2 = `M 0 ${50 - amplitude * Math.sin(offset)}`;
            
            for (let x = 0; x <= 100; x += 2) {
              const y1 = 50 + amplitude * Math.sin(x * frequency + time + offset);
              const y2 = 50 - amplitude * Math.sin(x * frequency + time + offset);
              path1 += ` L ${x} ${y1}`;
              path2 += ` L ${x} ${y2}`;
            }
            
            return (
              <g key={i} opacity={0.3 + Math.sin(time + offset) * 0.2}>
                <path
                  d={path1}
                  stroke="url(#helixGradient1)"
                  strokeWidth="1.5"
                  fill="none"
                  className="animate-pulse"
                  style={{ 
                    animationDelay: `${i * 0.2}s`,
                    filter: 'drop-shadow(0 0 3px rgba(59, 130, 246, 0.5))'
                  }}
                />
                <path
                  d={path2}
                  stroke="url(#helixGradient2)"
                  strokeWidth="1.5"
                  fill="none"
                  className="animate-pulse"
                  style={{ 
                    animationDelay: `${i * 0.2 + 0.5}s`,
                    filter: 'drop-shadow(0 0 3px rgba(236, 72, 153, 0.5))'
                  }}
                />
              </g>
            );
          })}
        </svg>
      )}
      
      {/* Particle System */}
      {particles.map(particle => {
        const opacity = 1 - (particle.life / particle.maxLife);
        const scale = 0.5 + (1 - particle.life / particle.maxLife) * 0.5;
        
        return (
          <div
            key={particle.id}
            className="absolute pointer-events-none"
            style={{
              left: particle.x,
              top: particle.y,
              transform: `translate(-50%, -50%) scale(${scale})`,
              opacity,
              zIndex: 10
            }}
          >
            {particle.type === 'nucleotide' && (
              <div className="w-2 h-2 rounded-full bg-gradient-to-r from-consciousness to-creativity animate-pulse border border-consciousness/50" />
            )}
            {particle.type === 'bond' && (
              <div className="w-4 h-px bg-gradient-to-r from-consciousness/60 to-creativity/60 animate-pulse" />
            )}
            {particle.type === 'helix' && (
              <div className="w-1 h-1 rounded-full bg-harmony animate-pulse shadow-lg shadow-harmony/50" />
            )}
          </div>
        );
      })}
      
      {/* Connection Lines */}
      {isHovering && (
        <svg className="absolute inset-0 w-full h-full" style={{ zIndex: 2 }}>
          {particles
            .filter(p => p.type === 'nucleotide')
            .map((particle, index, nucleotides) => {
              if (index === 0) return null;
              const prev = nucleotides[index - 1];
              const opacity = Math.min(
                1 - (particle.life / particle.maxLife),
                1 - (prev.life / prev.maxLife)
              );
              
              return (
                <line
                  key={`${particle.id}-${prev.id}`}
                  x1={prev.x}
                  y1={prev.y}
                  x2={particle.x}
                  y2={particle.y}
                  stroke="url(#helixGradient1)"
                  strokeWidth="0.5"
                  opacity={opacity * 0.6}
                  className="animate-pulse"
                />
              );
            })}
        </svg>
      )}
      
      {/* Quantum Field Effect */}
      {isHovering && (
        <div className="absolute inset-0 bg-gradient-radial from-consciousness/5 via-transparent to-creativity/5 animate-pulse" />
      )}
    </div>
  );
};

export default DNAStrandAnimation;
