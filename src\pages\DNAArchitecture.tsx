
import React, { useState } from 'react';
import Header from '../components/organisms/Header';
import Footer from '../components/organisms/Footer';
import Typography from '../components/atoms/Typography';
import Card from '../components/atoms/Card';
import Button from '../components/atoms/Button';
import DNAHelix from '../components/interactive/DNAHelix';
import BioQuantumFlow from '../components/interactive/BioQuantumFlow';
import { Dna, Network, Zap, Code } from 'lucide-react';

const DNAArchitecture: React.FC = () => {
  const [activeVisualization, setActiveVisualization] = useState<'dna' | 'bio'>('dna');

  return (
    <div className="min-h-screen surface-void">
      <Header />

      <main className="pt-24">
        <div className="container mx-auto px-8 py-16">
          <div className="text-center mb-16">
            <Typography as="h1" variant="4xl" weight="bold" gradient="consciousness" className="mb-6">
              DNA-Inspired Architecture
            </Typography>
            <Typography as="p" variant="xl" color="secondary" className="max-w-4xl mx-auto">
              Self-replicating systems that evolve and adapt through genetic algorithms and biological principles.
            </Typography>
          </div>

          {/* Interactive DNA Visualization */}
          <div className="mb-16">
            <Card variant="quantum" className="p-8 rounded-[32px] border-consciousness/25">
              <div className="text-center mb-8">
                <Typography variant="2xl" weight="semibold" color="consciousness" className="mb-4">
                  Interactive DNA Architecture
                </Typography>
                <div className="flex justify-center gap-4 mb-6">
                  <Button
                    variant={activeVisualization === 'dna' ? 'quantum' : 'outline-quantum'}
                    size="md"
                    onClick={() => setActiveVisualization('dna')}
                  >
                    DNA Helix
                  </Button>
                  <Button
                    variant={activeVisualization === 'bio' ? 'quantum' : 'outline-quantum'}
                    size="md"
                    onClick={() => setActiveVisualization('bio')}
                  >
                    Bio-Quantum Flow
                  </Button>
                </div>
              </div>

              <div className="h-96 rounded-[20px] overflow-hidden">
                {activeVisualization === 'dna' ? (
                  <DNAHelix
                    isActive={true}
                    intensity="intense"
                    color="consciousness"
                  />
                ) : (
                  <BioQuantumFlow
                    isActive={true}
                    variant="neural"
                    intensity="high"
                  />
                )}
              </div>
            </Card>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            <Card variant="consciousness" className="p-8 text-center rounded-[24px] border-consciousness/25">
              <Dna className="w-16 h-16 mx-auto mb-6 text-consciousness" />
              <Typography variant="lg" weight="semibold" color="consciousness" className="mb-4">
                Genetic Algorithms
              </Typography>
              <Typography variant="sm" color="secondary">
                Evolution-based optimization
              </Typography>
            </Card>

            <Card variant="creativity" className="p-8 text-center rounded-[24px] border-creativity/25">
              <Network className="w-16 h-16 mx-auto mb-6 text-creativity" />
              <Typography variant="lg" weight="semibold" color="creativity" className="mb-4">
                Self-Replication
              </Typography>
              <Typography variant="sm" color="secondary">
                Autonomous system reproduction
              </Typography>
            </Card>

            <Card variant="intuition" className="p-8 text-center rounded-[24px] border-harmony/25">
              <Code className="w-16 h-16 mx-auto mb-6 text-harmony" />
              <Typography variant="lg" weight="semibold" color="harmony" className="mb-4">
                Adaptive Mutations
              </Typography>
              <Typography variant="sm" color="secondary">
                Intelligent system improvements
              </Typography>
            </Card>

            <Card variant="neural" className="p-8 text-center rounded-[24px] border-consciousness/25">
              <Zap className="w-16 h-16 mx-auto mb-6 text-consciousness" />
              <Typography variant="lg" weight="semibold" color="consciousness" className="mb-4">
                Evolutionary Selection
              </Typography>
              <Typography variant="sm" color="secondary">
                Performance-based adaptation
              </Typography>
            </Card>
          </div>

          <div className="glass-quantum rounded-[32px] p-12 border border-consciousness/30">
            <Typography as="h2" variant="2xl" weight="semibold" color="consciousness" className="mb-8">
              Biological Intelligence Principles
            </Typography>
            <Typography variant="lg" color="secondary" className="leading-relaxed">
              Our DNA-inspired architecture mimics the fundamental principles of biological evolution. 
              Through genetic algorithms, self-replication, and adaptive mutations, our systems continuously 
              improve and evolve to meet changing requirements and environmental conditions.
            </Typography>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default DNAArchitecture;
