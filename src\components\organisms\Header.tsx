import React, { useState, useEffect } from 'react';
import { Menu, X, ChevronDown, Brain, Cpu, Network, Globe, Target, Rocket, Factory, TrendingUp, Users, Zap, Building2, Microscope, ArrowRightLeft, Shield, DollarSign, Handshake } from 'lucide-react';

const Button = ({ variant, size, className, onClick, children, ...props }) => {
  const baseClasses = "inline-flex items-center justify-center font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2";
  const variants = {
    quantum: "bg-gradient-to-r from-cyan-500/20 to-purple-500/20 text-white hover:from-cyan-400/30 hover:to-purple-400/30",
    default: "bg-gray-600 text-white hover:bg-gray-700"
  };
  const sizes = {
    sm: "px-3 py-1.5 text-sm",
    md: "px-4 py-2 text-base",
    lg: "px-6 py-3 text-lg"
  };
  
  return (
    <button
      className={`${baseClasses} ${variants[variant] || variants.default} ${sizes[size] || sizes.md} ${className}`}
      onClick={onClick}
      {...props}
    >
      {children}
    </button>
  );
};

const Typography = ({ variant, weight, className, children, ...props }) => {
  const variants = {
    xs: "text-xs",
    sm: "text-sm",
    base: "text-base",
    lg: "text-lg"
  };
  const weights = {
    normal: "font-normal",
    medium: "font-medium",
    semibold: "font-semibold",
    bold: "font-bold"
  };
  
  return (
    <span className={`${variants[variant] || variants.base} ${weights[weight] || weights.normal} ${className}`} {...props}>
      {children}
    </span>
  );
};

const cn = (...classes) => {
  return classes.filter(Boolean).join(' ');
};

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [isVisible, setIsVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      
      if (currentScrollY > lastScrollY && currentScrollY > 50) {
        setIsVisible(false);
      } else if (currentScrollY < lastScrollY) {
        setIsVisible(true);
      }
      
      setIsScrolled(currentScrollY > 10);
      setLastScrollY(currentScrollY);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [lastScrollY]);

  const technologyItems = [
    { label: 'ACI Overview', href: '/aci-technology', description: 'Artificial Cellular Intelligence' },
    { label: 'DNA-Inspired Architecture', href: '/dna-architecture', description: 'Self-replicating systems' },
    { label: 'Quantum Processing', href: '/quantum-processing', description: 'Advanced computation' },
    { label: 'Bio-Mimetic Networks', href: '/biomimetic-networks', description: 'Nature-inspired design' },
  ];

  const ecosystemItems = [
    { label: 'Platform Overview', href: '/ecosystem', description: 'Complete symbiotic system' },
    { label: 'Ecosystem Flywheel', href: '/ecosystem-flywheel', description: 'Synergistic value creation' },
    { label: 'Integration Layer', href: '/integration', description: 'Seamless connectivity' },
    { label: 'Sustainability', href: '/sustainability', description: 'Environmental benefits' },
  ];

  const impactItems = [
    { label: 'The Imperative', href: '/the-imperative', description: 'Why change is needed now' },
    { label: 'Global Vision', href: '/vision', description: 'Long-term transformation' },
    { label: 'Global Impact', href: '/symbioimpact', description: 'Worldwide transformation' },
    { label: 'About Us', href: '/about-us', description: 'Our mission and team' },
  ];

  const divisionsItems = [
    { label: 'SymbioCore', href: '/symbiocore', description: 'Core intelligence platform', icon: <Brain className="w-4 h-4" />, status: 'coming-soon' },
    { label: 'SymbioLabs', href: '/symbiolabs', description: 'Research & development hub', icon: <Microscope className="w-4 h-4" />, status: 'coming-soon' },
    { label: 'SymbioAutomate', href: '/symbioautomate', description: 'Intelligent automation suite', icon: <Factory className="w-4 h-4" />, status: 'active' },
    { label: 'SymbioXchange', href: '/symbioxchange', description: 'Global resource exchange', icon: <ArrowRightLeft className="w-4 h-4" />, status: 'coming-soon' },
    { label: 'SymbioEdge', href: '/symbioedge', description: 'Distributed edge intelligence', icon: <Network className="w-4 h-4" />, status: 'coming-soon' },
    { label: 'SymbioImpact', href: '/symbioimpact', description: 'Global impact platform', icon: <Shield className="w-4 h-4" />, status: 'coming-soon' },
    { label: 'SymbioVentures', href: '/symbioventures', description: 'Innovation investment', icon: <DollarSign className="w-4 h-4" />, status: 'coming-soon' },
    { label: 'SymbioAlliance', href: '/symbioalliance', description: 'Strategic partnerships', icon: <Handshake className="w-4 h-4" />, status: 'coming-soon' },
  ];

  const handleLogoClick = () => {
    window.location.href = '/';
  };

  const handleInterfaceClick = () => {
    window.location.href = '/interface';
  };

  const DropdownMenu = ({ items, title }) => (
    <div className="absolute top-full left-0 mt-4 w-[24rem] opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform group-hover:translate-y-0 translate-y-4 z-[100000]">
      <div className="relative bg-gradient-to-br from-gray-900/95 via-slate-800/98 to-gray-900/95 backdrop-blur-3xl rounded-3xl border border-cyan-300/20 shadow-2xl overflow-hidden">
        <div className="relative p-6 z-10">
          <div className="space-y-2">
            {items.map((item, index) => (
              <a
                key={item.label}
                href={item.href}
                className="group/item flex items-start space-x-4 p-4 rounded-2xl transition-all duration-300 relative overflow-hidden bg-gradient-to-r from-white/2 to-white/1 hover:from-cyan-400/8 hover:via-purple-500/5 hover:to-cyan-400/8 border border-white/5 hover:border-cyan-400/25"
              >
                <div className="relative w-10 h-10 rounded-full bg-gradient-to-br from-cyan-400/10 via-purple-500/5 to-cyan-400/10 flex items-center justify-center group-hover/item:from-cyan-400/20 group-hover/item:via-purple-500/15 group-hover/item:to-cyan-400/20 transition-all duration-300 border border-cyan-400/15 group-hover/item:border-cyan-400/30 backdrop-blur-sm">
                  <div className="relative z-10">
                    {item.icon || <div className="w-3 h-3 rounded-full bg-gradient-to-r from-cyan-400 to-purple-400 shadow-sm shadow-cyan-400/20" />}
                  </div>
                </div>
                
                <div className="flex-1 relative z-10">
                  <div className="flex items-center gap-3 mb-1">
                    <Typography 
                      variant="sm" 
                      weight="semibold" 
                      className="group-hover/item:bg-gradient-to-r group-hover/item:from-cyan-300 group-hover/item:via-white group-hover/item:to-purple-300 group-hover/item:bg-clip-text group-hover/item:text-transparent transition-all duration-300 text-white/90 font-mono tracking-wide"
                    >
                      {item.label}
                    </Typography>
                    {item.status === 'active' && (
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-cyan-400 shadow-lg shadow-cyan-400/50" />
                        <span className="px-3 py-1 text-xs bg-gradient-to-r from-cyan-400/20 to-cyan-400/30 text-cyan-300 rounded-full font-bold border border-cyan-400/30 backdrop-blur-sm font-mono">
                          ACTIVE
                        </span>
                      </div>
                    )}
                    {item.status === 'coming-soon' && (
                      <span className="px-3 py-1 text-xs bg-gradient-to-r from-purple-400/20 to-purple-400/30 text-purple-300 rounded-full font-bold border border-purple-400/30 backdrop-blur-sm font-mono">
                        INITIALIZING
                      </span>
                    )}
                  </div>
                  <Typography 
                    variant="xs" 
                    weight="normal"
                    className="leading-snug opacity-60 group-hover/item:opacity-85 transition-opacity duration-300 text-white/70 font-light tracking-wide"
                  >
                    {item.description}
                  </Typography>
                </div>
              </a>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <header className={cn(
      "fixed top-0 left-0 right-0 w-full z-[100000] transition-all duration-300 ease-out",
      "bg-gradient-to-r from-cyan-300/20 via-gray-800/90 to-black/95",
      "backdrop-blur-3xl border-b border-cyan-300/10",
      "shadow-2xl shadow-black/50",
      isScrolled ? "py-2" : "py-4",
      isVisible ? "translate-y-0" : "-translate-y-full"
    )}>
      <div className="w-full px-6 lg:px-8">
        {/* Force full width layout with CSS Grid - No container constraints */}
        <div className="w-full grid grid-cols-[1fr_auto_1fr] items-center gap-8 h-16">
          
          {/* Logo Section - Left Side - Flex Start */}
          <div className="flex justify-start items-center h-full">
            <div 
              className="flex items-center cursor-pointer group transition-all duration-300 ease-out hover:scale-[1.02]"
              onClick={handleLogoClick}
            >
              <div className="relative flex items-center justify-center w-64 h-16 transition-all duration-300 ease-out">
                <img 
                  src="/lovable-uploads/craiyon_160948_image.png" 
                  alt="SymbioWave" 
                  className="w-full h-full object-contain relative z-10 transition-all duration-300 ease-out group-hover:brightness-125"
                />
              </div>
            </div>
          </div>

          {/* Navigation - Center - Force center with flex */}
          <div className="flex items-center justify-center h-full">
            <div className={cn(
              "hidden lg:flex items-center justify-center space-x-6 transition-all duration-300 h-full"
            )}>
              
              {/* Divisions */}
              <div className="relative group z-[99999] flex items-center h-full">
                <button className={cn(
                  "flex items-center space-x-2 text-white/80 hover:text-white transition-all duration-300 ease-out rounded-2xl hover:bg-gradient-to-r hover:from-cyan-400/10 hover:via-purple-500/5 hover:to-cyan-400/10 border border-transparent hover:border-cyan-400/20 relative z-10 backdrop-blur-sm group/btn",
                  "px-4 py-2 text-sm"
                )}>
                  <div className="relative w-6 h-6 rounded-full bg-gradient-to-br from-cyan-400/20 to-purple-500/30 flex items-center justify-center transition-all duration-300 group-hover/btn:from-cyan-400/40 group-hover/btn:to-purple-500/50 border border-cyan-400/20 group-hover/btn:border-cyan-400/40 backdrop-blur-sm">
                    <Building2 className="w-3 h-3 text-cyan-300" />
                  </div>
                  <Typography variant="sm" weight="semibold" className="tracking-wide font-mono text-white/90 whitespace-nowrap">
                    Divisions
                  </Typography>
                  <ChevronDown className="w-3 h-3 transition-all duration-300 group-hover:rotate-180 text-white/60 group-hover:text-cyan-300" />
                </button>
                <DropdownMenu items={divisionsItems} title="" />
              </div>

              {/* ACI Technology */}
              <div className="relative group z-[99998] flex items-center h-full">
                <button className={cn(
                  "flex items-center space-x-2 text-white/80 hover:text-white transition-all duration-300 ease-out rounded-2xl hover:bg-gradient-to-r hover:from-cyan-400/10 hover:via-purple-500/5 hover:to-cyan-400/10 border border-transparent hover:border-cyan-400/20 relative z-10 backdrop-blur-sm group/btn",
                  "px-4 py-2 text-sm"
                )}>
                  <div className="relative w-6 h-6 rounded-full bg-gradient-to-br from-purple-400/20 to-cyan-500/30 flex items-center justify-center transition-all duration-300 group-hover/btn:from-purple-400/40 group-hover/btn:to-cyan-500/50 border border-purple-400/20 group-hover/btn:border-purple-400/40 backdrop-blur-sm">
                    <Cpu className="w-3 h-3 text-purple-300" />
                  </div>
                  <Typography variant="sm" weight="semibold" className="tracking-wide font-mono text-white/90 whitespace-nowrap">
                    ACI Tech
                  </Typography>
                  <ChevronDown className="w-3 h-3 transition-all duration-300 group-hover:rotate-180 text-white/60 group-hover:text-purple-300" />
                </button>
                <DropdownMenu items={technologyItems} title="" />
              </div>

              {/* Ecosystem */}
              <div className="relative group z-[99997] flex items-center h-full">
                <button className={cn(
                  "flex items-center space-x-2 text-white/80 hover:text-white transition-all duration-300 ease-out rounded-2xl hover:bg-gradient-to-r hover:from-cyan-400/10 hover:via-purple-500/5 hover:to-cyan-400/10 border border-transparent hover:border-cyan-400/20 relative z-10 backdrop-blur-sm group/btn",
                  "px-4 py-2 text-sm"
                )}>
                  <div className="relative w-6 h-6 rounded-full bg-gradient-to-br from-emerald-400/20 to-cyan-500/30 flex items-center justify-center transition-all duration-300 group-hover/btn:from-emerald-400/40 group-hover/btn:to-cyan-500/50 border border-emerald-400/20 group-hover/btn:border-emerald-400/40 backdrop-blur-sm">
                    <Globe className="w-3 h-3 text-emerald-300" />
                  </div>
                  <Typography variant="sm" weight="semibold" className="tracking-wide font-mono text-white/90 whitespace-nowrap">
                    Ecosystem
                  </Typography>
                  <ChevronDown className="w-3 h-3 transition-all duration-300 group-hover:rotate-180 text-white/60 group-hover:text-emerald-300" />
                </button>
                <DropdownMenu items={ecosystemItems} title="" />
              </div>

              {/* Impact */}
              <div className="relative group z-[99996] flex items-center h-full">
                <button className={cn(
                  "flex items-center space-x-2 text-white/80 hover:text-white transition-all duration-300 ease-out rounded-2xl hover:bg-gradient-to-r hover:from-cyan-400/10 hover:via-purple-500/5 hover:to-cyan-400/10 border border-transparent hover:border-cyan-400/20 relative z-10 backdrop-blur-sm group/btn",
                  "px-4 py-2 text-sm"
                )}>
                  <div className="relative w-6 h-6 rounded-full bg-gradient-to-br from-rose-400/20 to-orange-500/30 flex items-center justify-center transition-all duration-300 group-hover/btn:from-rose-400/40 group-hover/btn:to-orange-500/50 border border-rose-400/20 group-hover/btn:border-rose-400/40 backdrop-blur-sm">
                    <Target className="w-3 h-3 text-rose-300" />
                  </div>
                  <Typography variant="sm" weight="semibold" className="tracking-wide font-mono text-white/90 whitespace-nowrap">
                    Impact
                  </Typography>
                  <ChevronDown className="w-3 h-3 transition-all duration-300 group-hover:rotate-180 text-white/60 group-hover:text-rose-300" />
                </button>
                <DropdownMenu items={impactItems} title="" />
              </div>
            </div>
          </div>

          {/* Interface Button - Right Side - Flex End */}
          <div className="flex justify-end items-center h-full">
            {/* Desktop Interface Button */}
            <div className="hidden lg:block">
              <Button 
                variant="quantum" 
                size="md"
                className={cn(
                  "relative overflow-hidden group hover:scale-[1.04] transition-all duration-300 ease-out rounded-2xl border border-cyan-400/30 hover:border-cyan-400/60 bg-gradient-to-r from-cyan-400/10 via-purple-500/8 to-cyan-400/10 backdrop-blur-2xl",
                  "px-6 py-2 text-sm"
                )}
                onClick={handleInterfaceClick}
              >
                <span className="relative z-10 flex items-center space-x-3">
                  <Zap className="w-5 h-5 text-cyan-300 group-hover:text-cyan-200 transition-colors duration-300" />
                  <span className="font-semibold bg-gradient-to-r from-white via-cyan-200 to-white group-hover:from-cyan-100 group-hover:via-white group-hover:to-cyan-100 bg-clip-text text-transparent font-mono tracking-wide transition-all duration-300 text-base whitespace-nowrap">
                    Interface
                  </span>
                </span>
              </Button>
            </div>

            {/* Mobile Menu Button */}
            <button
              className={cn(
                "lg:hidden rounded-2xl bg-gradient-to-br from-black/90 to-gray-900/95 backdrop-blur-2xl border border-cyan-400/20 transition-all duration-300 ease-out hover:border-cyan-400/40 hover:bg-gradient-to-br hover:from-cyan-400/10 hover:to-purple-500/10 relative group",
                "p-3"
              )}
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? (
                <X className="w-5 h-5 text-cyan-300 relative z-10" />
              ) : (
                <Menu className="w-5 h-5 text-cyan-300 relative z-10" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div
          className={cn(
            'lg:hidden mt-4 space-y-4 transition-all duration-300 ease-out overflow-hidden rounded-3xl border border-cyan-400/20 backdrop-blur-3xl relative z-[100000]',
            isMenuOpen ? 'max-h-screen opacity-100 p-6' : 'max-h-0 opacity-0 p-0'
          )}
        >
          <div className="absolute inset-0 bg-gradient-to-r from-gray-900/95 via-slate-800/98 to-gray-900/95 rounded-3xl" />
          
          <div className="relative space-y-4 z-10">
            {divisionsItems.map((item) => (
              <a
                key={item.label}
                href={item.href}
                className="flex items-center space-x-4 py-4 px-5 text-white/80 hover:text-white transition-all duration-300 ease-out rounded-2xl hover:bg-gradient-to-r hover:from-cyan-400/10 hover:via-purple-500/5 hover:to-cyan-400/10 border border-transparent hover:border-cyan-400/25 group bg-gradient-to-r from-white/2 to-white/1 relative overflow-hidden"
                onClick={() => setIsMenuOpen(false)}
              >
                <div className="relative w-8 h-8 rounded-full bg-gradient-to-br from-cyan-400/15 via-purple-500/10 to-cyan-400/15 flex items-center justify-center border border-cyan-400/20 group-hover:scale-105 group-hover:border-cyan-400/40 transition-all duration-300 backdrop-blur-sm">
                  {item.icon}
                </div>
                
                <div className="flex-1">
                  <Typography variant="sm" weight="semibold" className="text-sm tracking-wide text-white font-mono group-hover:text-cyan-200 transition-colors duration-300">
                    {item.label}
                  </Typography>
                </div>
                
                {item.status === 'active' && (
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 rounded-full bg-cyan-400 shadow-sm shadow-cyan-400/50" />
                    <span className="px-3 py-1 text-xs bg-gradient-to-r from-cyan-400/20 to-cyan-400/30 text-cyan-300 rounded-full font-bold border border-cyan-400/30 backdrop-blur-sm font-mono">
                      ACTIVE
                    </span>
                  </div>
                )}
                {item.status === 'coming-soon' && (
                  <span className="px-3 py-1 text-xs bg-gradient-to-r from-purple-400/20 to-purple-400/30 text-purple-300 rounded-full font-bold border border-purple-400/30 backdrop-blur-sm font-mono">
                    INITIALIZING
                  </span>
                )}
              </a>
            ))}
            
            {/* Mobile Interface Button */}
            <div className="pt-6 border-t border-cyan-400/20 relative z-10">
              <Button 
                variant="quantum" 
                size="lg" 
                className="w-full rounded-2xl text-sm bg-gradient-to-r from-cyan-400/15 via-purple-500/10 to-cyan-400/15 border border-cyan-400/30 hover:border-cyan-400/50 backdrop-blur-2xl transition-all duration-300 hover:scale-[1.02] relative group overflow-hidden"
                onClick={() => {
                  setIsMenuOpen(false);
                  handleInterfaceClick();
                }}
              >
                <span className="flex items-center justify-center space-x-3 relative z-10">
                  <Zap className="w-4 h-4 text-cyan-300" />
                  <span className="bg-gradient-to-r from-white via-cyan-200 to-white bg-clip-text text-transparent font-semibold font-mono tracking-wide">
                    Interface
                  </span>
                </span>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;