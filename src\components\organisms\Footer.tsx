
import React from 'react';
import { ArrowRight } from 'lucide-react';
import Typography from '../atoms/Typography';
import Button from '../atoms/Button';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  const footerSections = [
    {
      title: "Ecosystem",
      links: [
        { name: "SymbioCore", href: "/symbiocore" },
        { name: "SymbioLabs", href: "/symbiolabs" },
        { name: "SymbioXchange", href: "/symbioxchange" },
        { name: "SymbioAutomate", href: "/symbioautomate" },
      ]
    },
    {
      title: "Solutions",
      links: [
        { name: "SymbioEdge", href: "/symbioedge" },
        { name: "SymbioImpact", href: "/symbioimpact" },
        { name: "SymbioVentures", href: "/symbioventures" },
        { name: "SymbioAlliance", href: "/symbioalliance" },
      ]
    },
    {
      title: "Resources",
      links: [
        { name: "ACI Technology", href: "/aci-technology" },
        { name: "Documentation", href: "/documentation" },
        { name: "Case Studies", href: "/case-studies" },
        { name: "Interface", href: "/interface" },
      ]
    },
    {
      title: "Company",
      links: [
        { name: "About Us", href: "/about-us" },
        { name: "Careers", href: "/careers" },
        { name: "Vision", href: "/vision" },
        { name: "The Imperative", href: "/the-imperative" },
      ]
    }
  ];

  return (
    <footer className="py-20 glass border-t border-subtle relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute bottom-0 left-1/4 w-64 h-64 bg-consciousness/5 rounded-cellular blur-3xl"></div>
        <div className="absolute top-0 right-1/4 w-48 h-48 bg-harmony/8 rounded-biomorphic blur-2xl"></div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        {/* Main Footer Content */}
        <div className="grid lg:grid-cols-5 gap-12 mb-16">
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <div className="flex items-center space-x-3 mb-6">
              <img 
                src="/lovable-uploads/69b2f8ba-ee9a-4a51-954a-923e57771f4d.png" 
                alt="SymbioWave" 
                className="w-12 h-12"
              />
              <Typography 
                variant="xl" 
                weight="bold" 
                font="display"
                color="consciousness"
              >
                SymbioWave
              </Typography>
            </div>

            <Typography 
              variant="sm" 
              color="secondary"
              className="mb-6 leading-relaxed max-w-md"
            >
              Pioneering the next economic paradigm through decentralized, adaptive, 
              and self-optimizing AI systems. Building the foundational infrastructure 
              for a symbiotic future.
            </Typography>

            <div className="mb-6 space-y-2">
              <Typography variant="sm" weight="semibold" color="consciousness" className="mb-3">
                Contact Information
              </Typography>
              <div className="space-y-1">
                <a href="mailto:<EMAIL>" className="block text-sm text-tertiary hover:text-consciousness transition-colors">
                  <EMAIL>
                </a>
                <a href="mailto:<EMAIL>" className="block text-sm text-tertiary hover:text-consciousness transition-colors">
                  <EMAIL>
                </a>
                <a href="mailto:<EMAIL>" className="block text-sm text-tertiary hover:text-consciousness transition-colors">
                  <EMAIL>
                </a>
              </div>
            </div>

            <Button 
              variant="outline-quantum" 
              size="sm"
              rightIcon={<ArrowRight className="w-4 h-4" />}
              onClick={() => window.location.href = '/careers'}
            >
              Join the Revolution
            </Button>
          </div>

          {/* Footer Links */}
          {footerSections.map((section) => (
            <div key={section.title}>
              <Typography 
                variant="sm" 
                weight="semibold" 
                color="consciousness"
                className="mb-4"
              >
                {section.title}
              </Typography>
              <ul className="space-y-3">
                {section.links.map((link) => (
                  <li key={link.name}>
                    <a 
                      href={link.href}
                      className="text-tertiary hover:text-consciousness transition-colors duration-quantum text-sm"
                    >
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-subtle pt-8 flex flex-col md:flex-row justify-between items-center">
          <Typography variant="xs" color="quaternary">
            © {currentYear} SymbioWave. All rights reserved. Building the future of symbiotic intelligence.
          </Typography>
          
          <div className="flex space-x-6 mt-4 md:mt-0">
            <Typography variant="xs" color="quaternary">
              AI Ethics Board Certified
            </Typography>
            <Typography variant="xs" color="quaternary">
              •
            </Typography>
            <Typography variant="xs" color="quaternary">
              Powered by ACI
            </Typography>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
